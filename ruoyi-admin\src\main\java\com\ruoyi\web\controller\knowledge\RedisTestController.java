package com.ruoyi.web.controller.knowledge;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.knowledge.util.RedisConnectionTestUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * Redis测试控制器
 * 用于测试Redis连接和RediSearch模块功能
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
@RestController
@RequestMapping("/knowledge/redis-test")
public class RedisTestController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(RedisTestController.class);

    @Autowired
    private RedisConnectionTestUtil redisConnectionTestUtil;

    @Value("${spring.redis.host:localhost}")
    private String redisHost;

    @Value("${spring.redis.port:6379}")
    private int redisPort;

    @Value("${spring.redis.database:0}")
    private int redisDatabase;

    @Value("${knowledge.embedding.store.type:memory}")
    private String storeType;

    @Value("${knowledge.embedding.store.redis.index-name:knowledge_base_index}")
    private String redisIndexName;

    /**
     * 获取Redis配置信息
     */
    @PreAuthorize("@ss.hasPermi('knowledge:redis:test')")
    @GetMapping("/config")
    public AjaxResult getRedisConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("storeType", storeType);
        config.put("redisHost", redisHost);
        config.put("redisPort", redisPort);
        config.put("redisDatabase", redisDatabase);
        config.put("redisIndexName", redisIndexName);
        
        return AjaxResult.success("Redis配置信息", config);
    }

    /**
     * 测试Redis基本连接
     */
    @PreAuthorize("@ss.hasPermi('knowledge:redis:test')")
    @PostMapping("/connection")
    public AjaxResult testConnection() {
        try {
            if (!"redis".equalsIgnoreCase(storeType)) {
                return AjaxResult.error("当前配置使用的是内存存储，不是Redis存储");
            }

            boolean success = redisConnectionTestUtil.testRedisConnection();
            if (success) {
                return AjaxResult.success("Redis连接测试成功");
            } else {
                return AjaxResult.error("Redis连接测试失败");
            }
        } catch (Exception e) {
            logger.error("Redis连接测试异常: {}", e.getMessage(), e);
            return AjaxResult.error("Redis连接测试异常: " + e.getMessage());
        }
    }

    /**
     * 测试RediSearch模块
     */
    @PreAuthorize("@ss.hasPermi('knowledge:redis:test')")
    @PostMapping("/search-module")
    public AjaxResult testSearchModule() {
        try {
            if (!"redis".equalsIgnoreCase(storeType)) {
                return AjaxResult.error("当前配置使用的是内存存储，不是Redis存储");
            }

            boolean success = redisConnectionTestUtil.testRediSearchModule();
            if (success) {
                return AjaxResult.success("RediSearch模块测试成功");
            } else {
                return AjaxResult.error("RediSearch模块测试失败，请检查是否安装了Redis Stack或RediSearch模块");
            }
        } catch (Exception e) {
            logger.error("RediSearch模块测试异常: {}", e.getMessage(), e);
            return AjaxResult.error("RediSearch模块测试异常: " + e.getMessage());
        }
    }

    /**
     * 获取Redis服务器信息
     */
    @PreAuthorize("@ss.hasPermi('knowledge:redis:test')")
    @GetMapping("/info")
    public AjaxResult getRedisInfo() {
        try {
            if (!"redis".equalsIgnoreCase(storeType)) {
                return AjaxResult.error("当前配置使用的是内存存储，不是Redis存储");
            }

            redisConnectionTestUtil.printRedisInfo();
            return AjaxResult.success("Redis信息已输出到日志，请查看控制台日志");
        } catch (Exception e) {
            logger.error("获取Redis信息异常: {}", e.getMessage(), e);
            return AjaxResult.error("获取Redis信息异常: " + e.getMessage());
        }
    }

    /**
     * 检查知识库索引状态
     */
    @PreAuthorize("@ss.hasPermi('knowledge:redis:test')")
    @GetMapping("/index-status")
    public AjaxResult checkIndexStatus() {
        try {
            if (!"redis".equalsIgnoreCase(storeType)) {
                return AjaxResult.error("当前配置使用的是内存存储，不是Redis存储");
            }

            redisConnectionTestUtil.checkKnowledgeBaseIndex(redisIndexName);
            return AjaxResult.success("索引状态信息已输出到日志，请查看控制台日志");
        } catch (Exception e) {
            logger.error("检查索引状态异常: {}", e.getMessage(), e);
            return AjaxResult.error("检查索引状态异常: " + e.getMessage());
        }
    }

    /**
     * 综合测试
     */
    @PreAuthorize("@ss.hasPermi('knowledge:redis:test')")
    @PostMapping("/comprehensive")
    public AjaxResult comprehensiveTest() {
        Map<String, Object> results = new HashMap<>();
        
        try {
            if (!"redis".equalsIgnoreCase(storeType)) {
                return AjaxResult.error("当前配置使用的是内存存储，不是Redis存储");
            }

            // 测试基本连接
            boolean connectionTest = redisConnectionTestUtil.testRedisConnection();
            results.put("connectionTest", connectionTest);

            if (connectionTest) {
                // 测试RediSearch模块
                boolean searchModuleTest = redisConnectionTestUtil.testRediSearchModule();
                results.put("searchModuleTest", searchModuleTest);

                // 获取Redis信息
                redisConnectionTestUtil.printRedisInfo();
                results.put("redisInfoLogged", true);

                // 检查索引状态
                redisConnectionTestUtil.checkKnowledgeBaseIndex(redisIndexName);
                results.put("indexStatusLogged", true);

                if (searchModuleTest) {
                    results.put("status", "success");
                    results.put("message", "所有测试通过，Redis和RediSearch模块工作正常");
                } else {
                    results.put("status", "partial");
                    results.put("message", "Redis连接正常，但RediSearch模块测试失败");
                }
            } else {
                results.put("status", "failed");
                results.put("message", "Redis连接失败");
            }

            return AjaxResult.success("综合测试完成", results);

        } catch (Exception e) {
            logger.error("综合测试异常: {}", e.getMessage(), e);
            results.put("status", "error");
            results.put("message", "测试过程中发生异常: " + e.getMessage());
            return AjaxResult.error("综合测试异常", results);
        }
    }
}
