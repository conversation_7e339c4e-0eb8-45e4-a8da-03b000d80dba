package com.ruoyi.knowledge.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;
import redis.clients.jedis.exceptions.JedisException;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;

/**
 * Redis连接测试工具类
 * 用于测试Redis连接和RediSearch模块是否正常工作
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
@Component
public class RedisConnectionTestUtil {

    private static final Logger logger = LoggerFactory.getLogger(RedisConnectionTestUtil.class);

    @Value("${spring.redis.host:localhost}")
    private String redisHost;

    @Value("${spring.redis.port:6379}")
    private int redisPort;

    @Value("${spring.redis.password:}")
    private String redisPassword;

    @Value("${spring.redis.database:0}")
    private int redisDatabase;

    @Value("${knowledge.embedding.store.type:memory}")
    private String storeType;

    private JedisPool jedisPool;

    @PostConstruct
    public void init() {
        if ("redis".equalsIgnoreCase(storeType)) {
            initJedisPool();
            testRedisConnection();
        }
    }

    /**
     * 初始化Jedis连接池
     */
    private void initJedisPool() {
        try {
            JedisPoolConfig config = new JedisPoolConfig();
            config.setMaxTotal(10);
            config.setMaxIdle(5);
            config.setMinIdle(1);
            config.setTestOnBorrow(true);
            config.setTestOnReturn(true);

            if (redisPassword != null && !redisPassword.trim().isEmpty()) {
                jedisPool = new JedisPool(config, redisHost, redisPort, 2000, redisPassword, redisDatabase);
            } else {
                jedisPool = new JedisPool(config, redisHost, redisPort, 2000, null, redisDatabase);
            }

            logger.info("Redis连接池初始化成功: {}:{}/{}", redisHost, redisPort, redisDatabase);
        } catch (Exception e) {
            logger.error("Redis连接池初始化失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 测试Redis连接
     */
    public boolean testRedisConnection() {
        if (jedisPool == null) {
            logger.warn("Redis连接池未初始化");
            return false;
        }

        try (Jedis jedis = jedisPool.getResource()) {
            // 测试基本连接
            String pong = jedis.ping();
            logger.info("Redis连接测试成功: {}", pong);

            // 测试数据库选择
            jedis.select(redisDatabase);
            logger.info("Redis数据库选择成功: {}", redisDatabase);

            // 测试基本操作
            jedis.set("test_key", "test_value");
            String value = jedis.get("test_key");
            jedis.del("test_key");
            logger.info("Redis基本操作测试成功: {}", value);

            return true;
        } catch (JedisException e) {
            logger.error("Redis连接测试失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 测试RediSearch模块
     */
    public boolean testRediSearchModule() {
        if (jedisPool == null) {
            logger.warn("Redis连接池未初始化");
            return false;
        }

        try (Jedis jedis = jedisPool.getResource()) {
            // 检查模块列表
            List<Object> modules = jedis.sendCommand(() -> "MODULE".getBytes(), "LIST".getBytes());
            logger.info("Redis模块列表: {}", modules);

            // 检查是否有search模块
            boolean hasSearchModule = false;
            for (Object module : modules) {
                if (module instanceof List) {
                    List<?> moduleInfo = (List<?>) module;
                    if (moduleInfo.size() >= 2 && "search".equals(moduleInfo.get(1))) {
                        hasSearchModule = true;
                        logger.info("发现RediSearch模块，版本: {}", moduleInfo.size() > 3 ? moduleInfo.get(3) : "未知");
                        break;
                    }
                }
            }

            if (!hasSearchModule) {
                logger.error("未找到RediSearch模块，请安装Redis Stack或添加RediSearch模块");
                return false;
            }

            // 测试创建简单索引
            String testIndexName = "test_vector_index";
            try {
                // 删除可能存在的测试索引
                jedis.sendCommand(() -> "FT.DROPINDEX".getBytes(), testIndexName.getBytes());
            } catch (Exception e) {
                // 忽略删除失败的错误
            }

            // 创建向量索引
            jedis.sendCommand(() -> "FT.CREATE".getBytes(), 
                testIndexName.getBytes(),
                "ON".getBytes(), "HASH".getBytes(),
                "PREFIX".getBytes(), "1".getBytes(), "test:".getBytes(),
                "SCHEMA".getBytes(),
                "vector".getBytes(), "VECTOR".getBytes(), "FLAT".getBytes(),
                "6".getBytes(), "TYPE".getBytes(), "FLOAT32".getBytes(),
                "DIM".getBytes(), "3".getBytes(),
                "DISTANCE_METRIC".getBytes(), "L2".getBytes());

            logger.info("RediSearch向量索引创建测试成功");

            // 清理测试索引
            jedis.sendCommand(() -> "FT.DROPINDEX".getBytes(), testIndexName.getBytes());
            logger.info("RediSearch模块测试完成");

            return true;
        } catch (Exception e) {
            logger.error("RediSearch模块测试失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取Redis信息
     */
    public void printRedisInfo() {
        if (jedisPool == null) {
            logger.warn("Redis连接池未初始化");
            return;
        }

        try (Jedis jedis = jedisPool.getResource()) {
            // 获取服务器信息
            String info = jedis.info("server");
            logger.info("Redis服务器信息:\n{}", info);

            // 获取内存信息
            String memoryInfo = jedis.info("memory");
            logger.info("Redis内存信息:\n{}", memoryInfo);

            // 获取模块信息
            List<Object> modules = jedis.sendCommand(() -> "MODULE".getBytes(), "LIST".getBytes());
            logger.info("Redis加载的模块: {}", modules);

        } catch (Exception e) {
            logger.error("获取Redis信息失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 检查知识库索引状态
     */
    public void checkKnowledgeBaseIndex(String indexName) {
        if (jedisPool == null) {
            logger.warn("Redis连接池未初始化");
            return;
        }

        try (Jedis jedis = jedisPool.getResource()) {
            // 获取索引信息
            List<Object> indexInfo = jedis.sendCommand(() -> "FT.INFO".getBytes(), indexName.getBytes());
            logger.info("知识库索引 {} 信息: {}", indexName, indexInfo);

        } catch (Exception e) {
            logger.warn("知识库索引 {} 不存在或获取信息失败: {}", indexName, e.getMessage());
        }
    }

    /**
     * 关闭连接池
     */
    public void close() {
        if (jedisPool != null && !jedisPool.isClosed()) {
            jedisPool.close();
            logger.info("Redis连接池已关闭");
        }
    }
}
