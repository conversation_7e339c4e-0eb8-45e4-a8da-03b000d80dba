package com.ruoyi.knowledge.service;

import java.util.List;
import java.util.Map;

/**
 * 知识库迁移服务接口
 * 用于从内存存储迁移到Redis存储
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
public interface IKnowledgeBaseMigrationService {

    /**
     * 获取所有需要迁移的知识库
     * 
     * @return 知识库ID列表
     */
    List<Long> getAllKnowledgeBasesToMigrate();

    /**
     * 迁移单个知识库
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 迁移结果
     */
    boolean migrateKnowledgeBase(Long knowledgeBaseId);

    /**
     * 批量迁移所有知识库
     * 
     * @return 迁移结果统计
     */
    Map<String, Object> migrateAllKnowledgeBases();

    /**
     * 验证知识库迁移结果
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 验证结果
     */
    boolean validateMigration(Long knowledgeBaseId);

    /**
     * 清理Redis中的所有向量数据
     * 
     * @return 清理结果
     */
    boolean clearRedisVectorData();

    /**
     * 获取迁移进度
     * 
     * @return 迁移进度信息
     */
    Map<String, Object> getMigrationProgress();

    /**
     * 重建知识库索引
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 重建结果
     */
    boolean rebuildKnowledgeBaseIndex(Long knowledgeBaseId);
}
