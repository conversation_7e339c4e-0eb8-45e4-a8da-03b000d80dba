CREATE DATABASE  IF NOT EXISTS `ry-vue` /*!40100 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci */ /*!80016 DEFAULT ENCRYPTION='N' */;
USE `ry-vue`;
-- MySQL dump 10.13  Distrib 8.0.42, for Win64 (x86_64)
--
-- Host: localhost    Database: ry-vue
-- ------------------------------------------------------
-- Server version	8.0.42

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `ai_chat_message`
--

DROP TABLE IF EXISTS `ai_chat_message`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ai_chat_message` (
  `message_id` varchar(64) NOT NULL COMMENT '消息ID',
  `session_id` varchar(64) NOT NULL COMMENT '会话ID',
  `role` varchar(20) NOT NULL COMMENT '消息角色（user/assistant）',
  `content` longtext NOT NULL COMMENT '消息内容',
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  `user_name` varchar(30) DEFAULT NULL COMMENT '用户名',
  `model` varchar(50) DEFAULT NULL COMMENT 'AI模型',
  `status` char(1) DEFAULT '0' COMMENT '消息状态（0正常 1删除）',
  `response_time` bigint DEFAULT NULL COMMENT '响应时间（毫秒）',
  `token_count` int DEFAULT NULL COMMENT 'Token数量',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`message_id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_role` (`role`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='AI聊天消息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ai_chat_message`
--

LOCK TABLES `ai_chat_message` WRITE;
/*!40000 ALTER TABLE `ai_chat_message` DISABLE KEYS */;
INSERT INTO `ai_chat_message` VALUES ('05017e881a6b467bb3ac049eec27f44a','96fb5448991144e7adc580332db42523','user','如何将mysql中的数据库导出来',1,'admin','qwen-plus','0',NULL,NULL,'admin','2025-07-10 11:21:51','',NULL,NULL),('22d360feee3e403f84ce6a5887f3e9e6','7645f01dcf1443b6ab36868237af4b88','user','你是谁',1,'admin','qwen-plus','1',NULL,NULL,'admin','2025-07-10 09:39:08','',NULL,NULL),('2598f40c143342bfa33be0eb9b8a4e60','96fb5448991144e7adc580332db42523','assistant','你好！在 MySQL 中导出数据库，最常用的方法是使用 `mysqldump` 工具。以下是基本的导出步骤：\n\n### 1. 打开终端或命令行工具\n\n### 2. 使用 `mysqldump` 导出数据库\n\n语法如下：\n\n```bash\nmysqldump -u [用户名] -p [数据库名] > [导出的文件名.sql]\n```\n\n示例：\n\n```bash\nmysqldump -u root -p mydatabase > backup.sql\n```\n\n系统会提示你输入密码，输入正确密码后就开始导出了。\n\n---\n\n### 常用选项说明：\n\n- `-u`：指定数据库用户名  \n- `-p`：表示需要输入密码  \n- `>`：将输出结果保存到文件  \n\n---\n\n### 如果你想导出所有数据库：\n\n```bash\nmysqldump -u root -p --all-databases > all_backup.sql\n```\n\n---\n\n导出完成后，`.sql` 文件就可以用于备份或导入到其他 MySQL 环境中了。\n\n如果你有更多具体需求（比如只导出结构、不导出数据等），也可以告诉我，我可以提供更详细的帮助 ?',NULL,NULL,'qwen-plus','0',8352,NULL,'system','2025-07-10 13:07:47','',NULL,NULL),('28339c9504234ce0b88e8efe8534b53a','96fb5448991144e7adc580332db42523','user','你是谁',1,'admin','qwen-plus','0',NULL,NULL,'admin','2025-07-10 11:07:21','',NULL,NULL),('3074150b23584ea78e5b067e63ff7bcd','7645f01dcf1443b6ab36868237af4b88','user','1+1=多少',1,'admin','qwen-plus','1',NULL,NULL,'admin','2025-07-10 09:57:38','',NULL,NULL),('3600a5b556454478b40cbe8aeb3966ef','7645f01dcf1443b6ab36868237af4b88','user','西安大学是211吗',1,'admin','qwen-plus','1',NULL,NULL,'admin','2025-07-10 10:10:32','',NULL,NULL),('36e8996134174716a4e4be076224991c','7645f01dcf1443b6ab36868237af4b88','assistant','你好！我是小若，一个智能助手。我可以帮助你回答问题、提供信息和完成各种任务。有什么我可以帮你的吗？',NULL,NULL,'qwen-plus','1',1584,NULL,'system','2025-07-10 09:39:10','',NULL,NULL),('383f4b4fded347a49fdc8deb09697177','96fb5448991144e7adc580332db42523','assistant','要将 MySQL 中的数据库导出，最常用的方法是使用 `mysqldump` 工具。以下是简洁明了的操作说明：\n\n---\n\n### ✅ 基本命令格式：\n```bash\nmysqldump -u [用户名] -p [数据库名] > [保存路径/文件名.sql]\n```\n\n#### 示例：\n```bash\nmysqldump -u root -p mydatabase > mydatabase_backup.sql\n```\n执行后输入密码即可导出。\n\n---\n\n### ? 导出所有数据库：\n```bash\nmysqldump -u root -p --all-databases > all_databases_backup.sql\n```\n\n---\n\n### ? 常用可选参数：\n- `--single-transaction`：保证数据一致性（适合 InnoDB）\n- `--routines`：导出存储过程和函数\n- `--events`：导出事件\n- `--no-data`：只导出结构不导出数据\n\n示例：\n```bash\nmysqldump -u root -p --single-transaction --routines mydatabase > mydatabase_backup.sql\n```\n\n---\n\n### ? 文件保存位置：\n默认保存在当前目录，你可以使用绝对路径指定其他位置，例如：\n```bash\nmysqldump -u root -p mydatabase > /home/<USER>/backup/mydatabase.sql\n```\n\n如需进一步帮助，比如导入数据库或压缩备份，也欢迎继续提问 ?',NULL,NULL,'qwen-plus','0',11460,NULL,'system','2025-07-10 11:22:02','',NULL,NULL),('44b35031a5f045b9af0b382795fd689c','7645f01dcf1443b6ab36868237af4b88','user','你是谁',1,'admin','qwen-plus','1',NULL,NULL,'admin','2025-07-10 09:55:36','',NULL,NULL),('4c1c4b5b4f324977903b36d98f5060f5','96fb5448991144e7adc580332db42523','user','如何将mysql中的数据库导出来',1,'admin','qwen-plus','0',NULL,NULL,'admin','2025-07-10 13:11:24','',NULL,NULL),('4c774bb51fce43f5b995d8987a6167ac','7645f01dcf1443b6ab36868237af4b88','assistant','我对这个名字没有具体的信息。如果你能提供更多背景或具体问题，我会尽力帮助你查找或解答。',NULL,NULL,'qwen-plus','1',917,NULL,'system','2025-07-10 09:40:34','',NULL,NULL),('4e5f934689834cc5ba49ec407009aae8','7645f01dcf1443b6ab36868237af4b88','assistant','西安大学不是211工程院校。根据公开资料，西安大学是一所地方性本科院校，未被列入国家“211工程”重点建设高校名单。\n\n如果你有其他关于学校选择或报考方面的问题，也欢迎继续提问！',NULL,NULL,'qwen-plus','1',2131,NULL,'system','2025-07-10 10:10:34','',NULL,NULL),('5119b0dc20c24ebdae6b28314860a58f','96fb5448991144e7adc580332db42523','assistant','在 MySQL 中，导出数据库最常用的方法是使用 `mysqldump` 工具。以下是具体操作步骤：\n\n### 一、使用 `mysqldump` 导出数据库\n\n#### 基本命令格式：\n```bash\nmysqldump -u [用户名] -p [数据库名] > [导出的文件名.sql]\n```\n\n#### 示例：\n```bash\nmysqldump -u root -p mydatabase > mydatabase_backup.sql\n```\n执行后会提示你输入密码，输入正确密码后即可开始导出。\n\n---\n\n### 二、导出所有数据库\n如果你想导出 MySQL 中所有的数据库，可以加上 `--all-databases` 参数：\n```bash\nmysqldump -u root -p --all-databases > all_databases_backup.sql\n```\n\n---\n\n### 三、附加常用选项\n- `--single-transaction`：用于一致性备份（适用于 InnoDB）\n- `--lock-tables=false`：不锁表（适合导出时不影响正在运行的服务）\n- `--routines`：导出存储过程和函数\n- `--events`：导出事件调度器\n\n例如：\n```bash\nmysqldump -u root -p --single-transaction mydatabase > mydatabase_backup.sql\n```\n\n---\n\n### 四、导出文件位置\n导出的 `.sql` 文件默认保存在当前终端所在的目录，你可以用 `pwd` 查看当前路径，或者指定完整路径保存，例如：\n```bash\nmysqldump -u root -p mydatabase > /home/<USER>/backups/mydatabase_backup.sql\n```\n\n---\n\n如果你需要导入数据库，也可以告诉我，我可以提供导入方法 ?',NULL,NULL,'qwen-plus','0',14550,NULL,'system','2025-07-10 11:18:56','',NULL,NULL),('54af10f1fde048c280e81924b342a859','96fb5448991144e7adc580332db42523','user','如何将mysql中的数据库导出来',1,'admin','qwen-plus','0',NULL,NULL,'admin','2025-07-10 13:07:39','',NULL,NULL),('57439b60f26b40e6be2e2ea303c5c574','96fb5448991144e7adc580332db42523','assistant','你好！导出 MySQL 数据库最常用的方法是使用命令行工具 `mysqldump`，以下是具体操作步骤：\n\n---\n\n### ✅ 使用 `mysqldump` 导出数据库\n\n#### 基本命令格式：\n```bash\nmysqldump -u 用户名 -p 数据库名 > 文件名.sql\n```\n\n#### 示例：\n```bash\nmysqldump -u root -p mydatabase > mydatabase_backup.sql\n```\n\n执行后会提示你输入密码，正确输入后就会将 `mydatabase` 导出为 `mydatabase_backup.sql` 文件。\n\n---\n\n### ? 导出所有数据库：\n如果你想一次性导出所有数据库，可以使用：\n```bash\nmysqldump -u root -p --all-databases > all_databases_backup.sql\n```\n\n---\n\n### ? 其他可选参数：\n\n- **只导出结构（不带数据）**：\n  ```bash\n  mysqldump -u root -p --no-data mydatabase > mydatabase_structure.sql\n  ```\n\n- **压缩导出（节省空间）**：\n  ```bash\n  mysqldump -u root -p mydatabase | gzip > mydatabase_backup.sql.gz\n  ```\n\n---\n\n### ?️ 如果你在用图形化工具（如 phpMyAdmin、Navicat 等）：\n\n- 打开工具 → 选择目标数据库 → 点击“导出”按钮 → 选择导出格式（通常选 SQL）→ 下载文件即可。\n\n---\n\n如果你告诉我你是用的 Windows 还是 Linux，或者是否使用图形界面，我可以提供更贴合你环境的操作说明哦 ?',NULL,NULL,'qwen-plus','0',16391,NULL,'system','2025-07-10 11:18:23','',NULL,NULL),('57bf7809534743728a1eaea6b9fd4f87','96fb5448991144e7adc580332db42523','assistant','在 MySQL 中，导出数据库的常用方法是使用 `mysqldump` 工具。这是一个命令行工具，可以将数据库结构和数据导出为 SQL 文件。以下是基本的导出步骤：\n\n### 1. 导出整个数据库\n\n```bash\nmysqldump -u 用户名 -p 数据库名 > 导出文件路径.sql\n```\n\n例如：\n\n```bash\nmysqldump -u root -p mydatabase > /path/to/backup/mydatabase.sql\n```\n\n系统会提示你输入密码。\n\n### 2. 导出多个数据库\n\n```bash\nmysqldump -u 用户名 -p --databases 数据库名1 数据库名2 > 导出文件路径.sql\n```\n\n### 3. 导出所有数据库\n\n```bash\nmysqldump -u 用户名 -p --all-databases > 导出文件路径.sql\n```\n\n### 4. 常用选项说明：\n\n- `-u`：指定数据库用户名\n- `-p`：表示需要输入密码（回车后会提示）\n- `--databases`：导出多个数据库\n- `--all-databases`：导出所有数据库\n- `>`：输出到文件\n\n---\n\n✅ **提示：**\n- 如果你在远程服务器上操作，请确保你有访问数据库的权限。\n- 导出的 `.sql` 文件可以用文本编辑器打开查看，也可以导入到另一个 MySQL 实例中。\n\n如果你需要导入这个备份文件，可以使用 `mysql` 命令进行导入。\n\n需要我告诉你如何导入吗？',NULL,NULL,'qwen-plus','0',15027,NULL,'system','2025-07-10 13:02:25','',NULL,NULL),('5e182e3c0ffd41e0affc1fe1eb2c38a5','7645f01dcf1443b6ab36868237af4b88','assistant','1 + 1 等于 2。',NULL,NULL,'qwen-plus','1',686,NULL,'system','2025-07-10 09:57:39','',NULL,NULL),('5e9f683ef9b943b7b5a3b5ff964a6b42','7645f01dcf1443b6ab36868237af4b88','assistant','你好！我是小若，一个智能助手。我可以帮助你回答问题、提供信息和完成各种任务。有什么我可以帮你的吗？',NULL,NULL,'qwen-plus','1',1702,NULL,'system','2025-07-10 09:45:16','',NULL,NULL),('6062b93cee034e7a8ea351ae636c3045','96fb5448991144e7adc580332db42523','assistant','在 MySQL 中导出数据库，最常用的方法是使用 `mysqldump` 工具。以下是几种常见方式：\n\n### 1. 导出整个数据库\n```bash\nmysqldump -u 用户名 -p 数据库名 > 导出文件路径.sql\n```\n例如：\n```bash\nmysqldump -u root -p mydatabase > /path/to/backup.sql\n```\n\n系统会提示你输入密码。\n\n---\n\n### 2. 导出多个数据库\n```bash\nmysqldump -u 用户名 -p --databases 数据库名1 数据库名2 > 导出文件路径.sql\n```\n\n---\n\n### 3. 导出所有数据库\n```bash\nmysqldump -u 用户名 -p --all-databases > 导出文件路径.sql\n```\n\n---\n\n### 4. 只导出表结构（不包含数据）\n添加 `-d` 参数：\n```bash\nmysqldump -u root -p -d mydatabase > backup_structure.sql\n```\n\n---\n\n### 5. 只导出数据（不包含结构）\n添加 `-t` 参数：\n```bash\nmysqldump -u root -p -t mydatabase > backup_data.sql\n```\n\n---\n\n### 小贴士：\n- 如果你在远程服务器上操作，可以将 `.sql` 文件导出到你喜欢的目录。\n- 使用 `source 备份文件.sql` 可以导入数据库。\n- 定期备份建议写成脚本 + crontab 自动执行。\n\n如果你有具体需求（比如只导出某些表、压缩保存等），欢迎继续问我 ?',NULL,NULL,'qwen-plus','0',13823,NULL,'system','2025-07-10 13:11:38','',NULL,NULL),('6de6122b855e46c8959bf37239f9c06a','96fb5448991144e7adc580332db42523','assistant','你好！将 MySQL 中的数据库导出，常用的方法有以下几种：\n\n### 1. 使用 `mysqldump` 命令行工具（推荐）\n这是最常用、最简单的方式。命令如下：\n\n```bash\nmysqldump -u 用户名 -p 数据库名 > 导出的文件名.sql\n```\n\n例如：\n```bash\nmysqldump -u root -p mydatabase > mydatabase_backup.sql\n```\n\n执行后会提示你输入密码，正确输入后即可导出。\n\n#### 导出所有数据库：\n```bash\nmysqldump -u root -p --all-databases > all_databases_backup.sql\n```\n\n---\n\n### 2. 使用 MySQL 客户端工具（如 phpMyAdmin、Navicat 等）\n\n- **phpMyAdmin**：登录后选择数据库 → 点击“导出” → 选择“快速”或“自定义”方式导出。\n- **Navicat / DBeaver**：右键数据库或表，选择“导出数据”或类似选项，按向导操作即可。\n\n---\n\n### 3. 使用 SQL 命令导出部分数据（如单个表或查询结果）\n\n你可以使用 `SELECT INTO OUTFILE` 来导出某个查询结果到文件中：\n\n```sql\nSELECT * FROM mytable\nINTO OUTFILE \'/tmp/mytable_data.csv\'\nFIELDS TERMINATED BY \',\' \nENCLOSED BY \'\"\'\nLINES TERMINATED BY \'\\n\';\n```\n\n注意：这种方式要求你有写入服务器文件系统的权限。\n\n---\n\n如果你告诉我你使用的具体环境（比如操作系统、是否用图形工具等），我可以给你更具体的指导哦 ?',NULL,NULL,'qwen-plus','0',14828,NULL,'system','2025-07-10 11:17:56','',NULL,NULL),('6e0e5f794903443bb82dcab11dfd9b5d','7645f01dcf1443b6ab36868237af4b88','assistant','你好！我是小若，一个智能助手。我可以帮助你回答问题、提供信息和完成各种任务。有什么我可以帮你的吗？',NULL,NULL,'qwen-plus','1',3183,NULL,'system','2025-07-10 09:55:39','',NULL,NULL),('82d0411c989e40c78446c44fd79563e5','96fb5448991144e7adc580332db42523','user','你知道如何和你对话采用流式输出吗',1,'admin','qwen-plus','0',NULL,NULL,'admin','2025-07-10 11:15:07','',NULL,NULL),('86f3c304124e4b77938f774d8fae2f43','96fb5448991144e7adc580332db42523','assistant','当然知道！流式输出是指在对话中逐步生成回复内容，而不是一次性输出全部内容。如果你希望我使用流式输出来回应你的问题，请告诉我，我会尽量以这种方式进行交流。\n\n不过需要注意的是，目前你在网页端或应用端与我交互时，通常是以一次性输出的形式获得回复。流式输出更多是在特定场景下（如语音助手、实时对话等）体现的一种交互方式。\n\n如果你想测试或体验流式输出的效果，可以告诉我，我会尽可能模拟这种交互风格。还有其他需要了解的吗？',NULL,NULL,'qwen-plus','0',5171,NULL,'system','2025-07-10 11:15:12','',NULL,NULL),('9707e91871de4e469c7734c2d2cd6508','96fb5448991144e7adc580332db42523','assistant','你看到的是 `mysqldump` 命令的基本用法说明。它提示了三种常见的使用方式：\n\n---\n\n### ✅ 1. 导出单个数据库或其中的某些表：\n```bash\nmysqldump [OPTIONS] database [tables]\n```\n**示例：**\n```bash\nmysqldump -u root -p mydb > mydb_backup.sql          # 导出整个数据库\nmysqldump -u root -p mydb mytable > mytable.sql      # 只导出某个表\n```\n\n---\n\n### ✅ 2. 导出多个指定的数据库：\n```bash\nmysqldump [OPTIONS] --databases DB1 [DB2 DB3...]\n```\n**示例：**\n```bash\nmysqldump -u root -p --databases db1 db2 > backup.sql\n```\n\n---\n\n### ✅ 3. 导出所有数据库：\n```bash\nmysqldump [OPTIONS] --all-databases [OPTIONS]\n```\n**示例：**\n```bash\nmysqldump -u root -p --all-databases > full_backup.sql\n```\n\n---\n\n? **常用选项说明：**\n\n| 参数 | 含义 |\n|------|------|\n| `-u` | 指定用户名（如：-u root）|\n| `-p` | 表示需要输入密码（运行后会提示输入）|\n| `-d` | 只导出结构，不导数据 |\n| `-t` | 只导出数据，不导结构 |\n| `--single-transaction` | 对 InnoDB 表进行一致性备份，不锁表 |\n\n---\n\n? 如果你想查看完整参数列表，可以运行：\n```bash\nmysqldump --help\n```\n\n如果你有具体目标（例如定时备份、压缩导出等），我可以帮你写命令或脚本 ?',NULL,NULL,'qwen-plus','0',14480,NULL,'system','2025-07-10 13:17:00','',NULL,NULL),('9ebb6571c6884f93bbe1faa8296b5e24','7645f01dcf1443b6ab36868237af4b88','user','你是谁',1,'admin','qwen-plus','1',NULL,NULL,'admin','2025-07-10 09:45:14','',NULL,NULL),('a9f8f17db73647d48429eee201aa6378','96fb5448991144e7adc580332db42523','user','如何将mysql中的数据库导出来',1,'admin','qwen-plus','0',NULL,NULL,'admin','2025-07-10 11:18:07','',NULL,NULL),('b1c8794077504cd2b7fa28904378ac66','7645f01dcf1443b6ab36868237af4b88','user','你知道东哥吗',1,'admin','qwen-plus','1',NULL,NULL,'admin','2025-07-10 09:39:28','',NULL,NULL),('c11db5397f17431fabf71ece1cea054b','96fb5448991144e7adc580332db42523','user','Usage: mysqldump [OPTIONS] database [tables]\nOR     mysqldump [OPTIONS] --databases [OPTIONS] DB1 [DB2 DB3...]\nOR     mysqldump [OPTIONS] --all-databases [OPTIONS]\nFor more options, use mysqldump --help\n',1,'admin','qwen-plus','0',NULL,NULL,'admin','2025-07-10 13:16:45','',NULL,NULL),('cc74c151d0c94d5c86e3af31ef4f8910','96fb5448991144e7adc580332db42523','user','如何将mysql中的数据库导出来',1,'admin','qwen-plus','0',NULL,NULL,'admin','2025-07-10 11:17:41','',NULL,NULL),('cee6a1ab7cac486097cd643cc99dc30d','7645f01dcf1443b6ab36868237af4b88','assistant','你好，如果你是指京东集团创始人刘强东，我可以提供一些公开的信息。刘强东是中国著名的企业家，他创立了京东集团，这是一家知名的电子商务和科技公司。如果你有具体的问题或需要了解的内容，可以告诉我，我会尽力提供帮助。',NULL,NULL,'qwen-plus','1',3608,NULL,'system','2025-07-10 09:39:32','',NULL,NULL),('dd1346e4e1854ec4908bc94f6f1d0c4f','96fb5448991144e7adc580332db42523','user','如何将mysql中的数据库导出来',1,'admin','qwen-plus','0',NULL,NULL,'admin','2025-07-10 11:18:41','',NULL,NULL),('ddc033e473244de18a6e2b6517c1b631','96fb5448991144e7adc580332db42523','user','如何将mysql中的数据库导出来',1,'admin','qwen-plus','0',NULL,NULL,'admin','2025-07-10 13:02:10','',NULL,NULL),('f5dbfe8bde0e4d1487316fbbc8cf69d2','7645f01dcf1443b6ab36868237af4b88','user','你知道陈冠宏吗',1,'admin','qwen-plus','1',NULL,NULL,'admin','2025-07-10 09:40:33','',NULL,NULL),('ffefd57feb674f9fa392615c32674c65','96fb5448991144e7adc580332db42523','assistant','你好！我是小若，你的智能助手。我可以帮助你解答各种问题、提供信息和协助处理任务。有什么我可以帮你的吗？',NULL,NULL,'qwen-plus','0',1877,NULL,'system','2025-07-10 11:07:23','',NULL,NULL);
/*!40000 ALTER TABLE `ai_chat_message` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ai_chat_session`
--

DROP TABLE IF EXISTS `ai_chat_session`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ai_chat_session` (
  `session_id` varchar(64) NOT NULL COMMENT '会话ID',
  `title` varchar(100) DEFAULT '新对话' COMMENT '会话标题',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `user_name` varchar(30) NOT NULL COMMENT '用户名',
  `model` varchar(50) DEFAULT 'qwen-plus' COMMENT 'AI模型',
  `status` char(1) DEFAULT '0' COMMENT '会话状态（0正常 1停用）',
  `last_active_time` datetime DEFAULT NULL COMMENT '最后活跃时间',
  `message_count` int DEFAULT '0' COMMENT '消息数量',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`session_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_last_active_time` (`last_active_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='AI聊天会话表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ai_chat_session`
--

LOCK TABLES `ai_chat_session` WRITE;
/*!40000 ALTER TABLE `ai_chat_session` DISABLE KEYS */;
INSERT INTO `ai_chat_session` VALUES ('96fb5448991144e7adc580332db42523','新对话',1,'admin','qwen-plus','0','2025-07-10 13:16:59',20,'admin','2025-07-10 10:26:45','',NULL,NULL);
/*!40000 ALTER TABLE `ai_chat_session` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `gen_table`
--

DROP TABLE IF EXISTS `gen_table`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `gen_table` (
  `table_id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `table_name` varchar(200) DEFAULT '' COMMENT '表名称',
  `table_comment` varchar(500) DEFAULT '' COMMENT '表描述',
  `sub_table_name` varchar(64) DEFAULT NULL COMMENT '关联子表的表名',
  `sub_table_fk_name` varchar(64) DEFAULT NULL COMMENT '子表关联的外键名',
  `class_name` varchar(100) DEFAULT '' COMMENT '实体类名称',
  `tpl_category` varchar(200) DEFAULT 'crud' COMMENT '使用的模板（crud单表操作 tree树表操作）',
  `tpl_web_type` varchar(30) DEFAULT '' COMMENT '前端模板类型（element-ui模版 element-plus模版）',
  `package_name` varchar(100) DEFAULT NULL COMMENT '生成包路径',
  `module_name` varchar(30) DEFAULT NULL COMMENT '生成模块名',
  `business_name` varchar(30) DEFAULT NULL COMMENT '生成业务名',
  `function_name` varchar(50) DEFAULT NULL COMMENT '生成功能名',
  `function_author` varchar(50) DEFAULT NULL COMMENT '生成功能作者',
  `gen_type` char(1) DEFAULT '0' COMMENT '生成代码方式（0zip压缩包 1自定义路径）',
  `gen_path` varchar(200) DEFAULT '/' COMMENT '生成路径（不填默认项目路径）',
  `options` varchar(1000) DEFAULT NULL COMMENT '其它生成选项',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`table_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='代码生成业务表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `gen_table`
--

LOCK TABLES `gen_table` WRITE;
/*!40000 ALTER TABLE `gen_table` DISABLE KEYS */;
/*!40000 ALTER TABLE `gen_table` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `gen_table_column`
--

DROP TABLE IF EXISTS `gen_table_column`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `gen_table_column` (
  `column_id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `table_id` bigint DEFAULT NULL COMMENT '归属表编号',
  `column_name` varchar(200) DEFAULT NULL COMMENT '列名称',
  `column_comment` varchar(500) DEFAULT NULL COMMENT '列描述',
  `column_type` varchar(100) DEFAULT NULL COMMENT '列类型',
  `java_type` varchar(500) DEFAULT NULL COMMENT 'JAVA类型',
  `java_field` varchar(200) DEFAULT NULL COMMENT 'JAVA字段名',
  `is_pk` char(1) DEFAULT NULL COMMENT '是否主键（1是）',
  `is_increment` char(1) DEFAULT NULL COMMENT '是否自增（1是）',
  `is_required` char(1) DEFAULT NULL COMMENT '是否必填（1是）',
  `is_insert` char(1) DEFAULT NULL COMMENT '是否为插入字段（1是）',
  `is_edit` char(1) DEFAULT NULL COMMENT '是否编辑字段（1是）',
  `is_list` char(1) DEFAULT NULL COMMENT '是否列表字段（1是）',
  `is_query` char(1) DEFAULT NULL COMMENT '是否查询字段（1是）',
  `query_type` varchar(200) DEFAULT 'EQ' COMMENT '查询方式（等于、不等于、大于、小于、范围）',
  `html_type` varchar(200) DEFAULT NULL COMMENT '显示类型（文本框、文本域、下拉框、复选框、单选框、日期控件）',
  `dict_type` varchar(200) DEFAULT '' COMMENT '字典类型',
  `sort` int DEFAULT NULL COMMENT '排序',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`column_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='代码生成业务表字段';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `gen_table_column`
--

LOCK TABLES `gen_table_column` WRITE;
/*!40000 ALTER TABLE `gen_table_column` DISABLE KEYS */;
/*!40000 ALTER TABLE `gen_table_column` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qrtz_blob_triggers`
--

DROP TABLE IF EXISTS `qrtz_blob_triggers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_blob_triggers` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `blob_data` blob COMMENT '存放持久化Trigger对象',
  PRIMARY KEY (`sched_name`,`trigger_name`,`trigger_group`),
  CONSTRAINT `qrtz_blob_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Blob类型的触发器表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qrtz_blob_triggers`
--

LOCK TABLES `qrtz_blob_triggers` WRITE;
/*!40000 ALTER TABLE `qrtz_blob_triggers` DISABLE KEYS */;
/*!40000 ALTER TABLE `qrtz_blob_triggers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qrtz_calendars`
--

DROP TABLE IF EXISTS `qrtz_calendars`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_calendars` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `calendar_name` varchar(200) NOT NULL COMMENT '日历名称',
  `calendar` blob NOT NULL COMMENT '存放持久化calendar对象',
  PRIMARY KEY (`sched_name`,`calendar_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='日历信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qrtz_calendars`
--

LOCK TABLES `qrtz_calendars` WRITE;
/*!40000 ALTER TABLE `qrtz_calendars` DISABLE KEYS */;
/*!40000 ALTER TABLE `qrtz_calendars` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qrtz_cron_triggers`
--

DROP TABLE IF EXISTS `qrtz_cron_triggers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_cron_triggers` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `cron_expression` varchar(200) NOT NULL COMMENT 'cron表达式',
  `time_zone_id` varchar(80) DEFAULT NULL COMMENT '时区',
  PRIMARY KEY (`sched_name`,`trigger_name`,`trigger_group`),
  CONSTRAINT `qrtz_cron_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Cron类型的触发器表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qrtz_cron_triggers`
--

LOCK TABLES `qrtz_cron_triggers` WRITE;
/*!40000 ALTER TABLE `qrtz_cron_triggers` DISABLE KEYS */;
/*!40000 ALTER TABLE `qrtz_cron_triggers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qrtz_fired_triggers`
--

DROP TABLE IF EXISTS `qrtz_fired_triggers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_fired_triggers` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `entry_id` varchar(95) NOT NULL COMMENT '调度器实例id',
  `trigger_name` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `instance_name` varchar(200) NOT NULL COMMENT '调度器实例名',
  `fired_time` bigint NOT NULL COMMENT '触发的时间',
  `sched_time` bigint NOT NULL COMMENT '定时器制定的时间',
  `priority` int NOT NULL COMMENT '优先级',
  `state` varchar(16) NOT NULL COMMENT '状态',
  `job_name` varchar(200) DEFAULT NULL COMMENT '任务名称',
  `job_group` varchar(200) DEFAULT NULL COMMENT '任务组名',
  `is_nonconcurrent` varchar(1) DEFAULT NULL COMMENT '是否并发',
  `requests_recovery` varchar(1) DEFAULT NULL COMMENT '是否接受恢复执行',
  PRIMARY KEY (`sched_name`,`entry_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='已触发的触发器表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qrtz_fired_triggers`
--

LOCK TABLES `qrtz_fired_triggers` WRITE;
/*!40000 ALTER TABLE `qrtz_fired_triggers` DISABLE KEYS */;
/*!40000 ALTER TABLE `qrtz_fired_triggers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qrtz_job_details`
--

DROP TABLE IF EXISTS `qrtz_job_details`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_job_details` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `job_name` varchar(200) NOT NULL COMMENT '任务名称',
  `job_group` varchar(200) NOT NULL COMMENT '任务组名',
  `description` varchar(250) DEFAULT NULL COMMENT '相关介绍',
  `job_class_name` varchar(250) NOT NULL COMMENT '执行任务类名称',
  `is_durable` varchar(1) NOT NULL COMMENT '是否持久化',
  `is_nonconcurrent` varchar(1) NOT NULL COMMENT '是否并发',
  `is_update_data` varchar(1) NOT NULL COMMENT '是否更新数据',
  `requests_recovery` varchar(1) NOT NULL COMMENT '是否接受恢复执行',
  `job_data` blob COMMENT '存放持久化job对象',
  PRIMARY KEY (`sched_name`,`job_name`,`job_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='任务详细信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qrtz_job_details`
--

LOCK TABLES `qrtz_job_details` WRITE;
/*!40000 ALTER TABLE `qrtz_job_details` DISABLE KEYS */;
/*!40000 ALTER TABLE `qrtz_job_details` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qrtz_locks`
--

DROP TABLE IF EXISTS `qrtz_locks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_locks` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `lock_name` varchar(40) NOT NULL COMMENT '悲观锁名称',
  PRIMARY KEY (`sched_name`,`lock_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='存储的悲观锁信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qrtz_locks`
--

LOCK TABLES `qrtz_locks` WRITE;
/*!40000 ALTER TABLE `qrtz_locks` DISABLE KEYS */;
/*!40000 ALTER TABLE `qrtz_locks` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qrtz_paused_trigger_grps`
--

DROP TABLE IF EXISTS `qrtz_paused_trigger_grps`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_paused_trigger_grps` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `trigger_group` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  PRIMARY KEY (`sched_name`,`trigger_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='暂停的触发器表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qrtz_paused_trigger_grps`
--

LOCK TABLES `qrtz_paused_trigger_grps` WRITE;
/*!40000 ALTER TABLE `qrtz_paused_trigger_grps` DISABLE KEYS */;
/*!40000 ALTER TABLE `qrtz_paused_trigger_grps` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qrtz_scheduler_state`
--

DROP TABLE IF EXISTS `qrtz_scheduler_state`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_scheduler_state` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `instance_name` varchar(200) NOT NULL COMMENT '实例名称',
  `last_checkin_time` bigint NOT NULL COMMENT '上次检查时间',
  `checkin_interval` bigint NOT NULL COMMENT '检查间隔时间',
  PRIMARY KEY (`sched_name`,`instance_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='调度器状态表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qrtz_scheduler_state`
--

LOCK TABLES `qrtz_scheduler_state` WRITE;
/*!40000 ALTER TABLE `qrtz_scheduler_state` DISABLE KEYS */;
/*!40000 ALTER TABLE `qrtz_scheduler_state` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qrtz_simple_triggers`
--

DROP TABLE IF EXISTS `qrtz_simple_triggers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_simple_triggers` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `repeat_count` bigint NOT NULL COMMENT '重复的次数统计',
  `repeat_interval` bigint NOT NULL COMMENT '重复的间隔时间',
  `times_triggered` bigint NOT NULL COMMENT '已经触发的次数',
  PRIMARY KEY (`sched_name`,`trigger_name`,`trigger_group`),
  CONSTRAINT `qrtz_simple_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='简单触发器的信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qrtz_simple_triggers`
--

LOCK TABLES `qrtz_simple_triggers` WRITE;
/*!40000 ALTER TABLE `qrtz_simple_triggers` DISABLE KEYS */;
/*!40000 ALTER TABLE `qrtz_simple_triggers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qrtz_simprop_triggers`
--

DROP TABLE IF EXISTS `qrtz_simprop_triggers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_simprop_triggers` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `str_prop_1` varchar(512) DEFAULT NULL COMMENT 'String类型的trigger的第一个参数',
  `str_prop_2` varchar(512) DEFAULT NULL COMMENT 'String类型的trigger的第二个参数',
  `str_prop_3` varchar(512) DEFAULT NULL COMMENT 'String类型的trigger的第三个参数',
  `int_prop_1` int DEFAULT NULL COMMENT 'int类型的trigger的第一个参数',
  `int_prop_2` int DEFAULT NULL COMMENT 'int类型的trigger的第二个参数',
  `long_prop_1` bigint DEFAULT NULL COMMENT 'long类型的trigger的第一个参数',
  `long_prop_2` bigint DEFAULT NULL COMMENT 'long类型的trigger的第二个参数',
  `dec_prop_1` decimal(13,4) DEFAULT NULL COMMENT 'decimal类型的trigger的第一个参数',
  `dec_prop_2` decimal(13,4) DEFAULT NULL COMMENT 'decimal类型的trigger的第二个参数',
  `bool_prop_1` varchar(1) DEFAULT NULL COMMENT 'Boolean类型的trigger的第一个参数',
  `bool_prop_2` varchar(1) DEFAULT NULL COMMENT 'Boolean类型的trigger的第二个参数',
  PRIMARY KEY (`sched_name`,`trigger_name`,`trigger_group`),
  CONSTRAINT `qrtz_simprop_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='同步机制的行锁表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qrtz_simprop_triggers`
--

LOCK TABLES `qrtz_simprop_triggers` WRITE;
/*!40000 ALTER TABLE `qrtz_simprop_triggers` DISABLE KEYS */;
/*!40000 ALTER TABLE `qrtz_simprop_triggers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qrtz_triggers`
--

DROP TABLE IF EXISTS `qrtz_triggers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_triggers` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) NOT NULL COMMENT '触发器的名字',
  `trigger_group` varchar(200) NOT NULL COMMENT '触发器所属组的名字',
  `job_name` varchar(200) NOT NULL COMMENT 'qrtz_job_details表job_name的外键',
  `job_group` varchar(200) NOT NULL COMMENT 'qrtz_job_details表job_group的外键',
  `description` varchar(250) DEFAULT NULL COMMENT '相关介绍',
  `next_fire_time` bigint DEFAULT NULL COMMENT '上一次触发时间（毫秒）',
  `prev_fire_time` bigint DEFAULT NULL COMMENT '下一次触发时间（默认为-1表示不触发）',
  `priority` int DEFAULT NULL COMMENT '优先级',
  `trigger_state` varchar(16) NOT NULL COMMENT '触发器状态',
  `trigger_type` varchar(8) NOT NULL COMMENT '触发器的类型',
  `start_time` bigint NOT NULL COMMENT '开始时间',
  `end_time` bigint DEFAULT NULL COMMENT '结束时间',
  `calendar_name` varchar(200) DEFAULT NULL COMMENT '日程表名称',
  `misfire_instr` smallint DEFAULT NULL COMMENT '补偿执行的策略',
  `job_data` blob COMMENT '存放持久化job对象',
  PRIMARY KEY (`sched_name`,`trigger_name`,`trigger_group`),
  KEY `sched_name` (`sched_name`,`job_name`,`job_group`),
  CONSTRAINT `qrtz_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `job_name`, `job_group`) REFERENCES `qrtz_job_details` (`sched_name`, `job_name`, `job_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='触发器详细信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qrtz_triggers`
--

LOCK TABLES `qrtz_triggers` WRITE;
/*!40000 ALTER TABLE `qrtz_triggers` DISABLE KEYS */;
/*!40000 ALTER TABLE `qrtz_triggers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_config`
--

DROP TABLE IF EXISTS `sys_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_config` (
  `config_id` int NOT NULL AUTO_INCREMENT COMMENT '参数主键',
  `config_name` varchar(100) DEFAULT '' COMMENT '参数名称',
  `config_key` varchar(100) DEFAULT '' COMMENT '参数键名',
  `config_value` varchar(500) DEFAULT '' COMMENT '参数键值',
  `config_type` char(1) DEFAULT 'N' COMMENT '系统内置（Y是 N否）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`config_id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='参数配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_config`
--

LOCK TABLES `sys_config` WRITE;
/*!40000 ALTER TABLE `sys_config` DISABLE KEYS */;
INSERT INTO `sys_config` VALUES (1,'主框架页-默认皮肤样式名称','sys.index.skinName','skin-blue','Y','admin','2025-07-07 12:40:28','',NULL,'蓝色 skin-blue、绿色 skin-green、紫色 skin-purple、红色 skin-red、黄色 skin-yellow'),(2,'用户管理-账号初始密码','sys.user.initPassword','123456','Y','admin','2025-07-07 12:40:28','',NULL,'初始化密码 123456'),(3,'主框架页-侧边栏主题','sys.index.sideTheme','theme-dark','Y','admin','2025-07-07 12:40:28','',NULL,'深色主题theme-dark，浅色主题theme-light'),(4,'账号自助-验证码开关','sys.account.captchaEnabled','true','Y','admin','2025-07-07 12:40:28','',NULL,'是否开启验证码功能（true开启，false关闭）'),(5,'账号自助-是否开启用户注册功能','sys.account.registerUser','true','Y','admin','2025-07-07 12:40:28','admin','2025-07-07 12:57:22','是否开启注册用户功能（true开启，false关闭）'),(6,'用户登录-黑名单列表','sys.login.blackIPList','','Y','admin','2025-07-07 12:40:28','',NULL,'设置登录IP黑名单限制，多个匹配项以;分隔，支持匹配（*通配、网段）'),(7,'用户管理-初始密码修改策略','sys.account.initPasswordModify','1','Y','admin','2025-07-07 12:40:28','',NULL,'0：初始密码修改策略关闭，没有任何提示，1：提醒用户，如果未修改初始密码，则在登录时就会提醒修改密码对话框'),(8,'用户管理-账号密码更新周期','sys.account.passwordValidateDays','0','Y','admin','2025-07-07 12:40:28','',NULL,'密码更新周期（填写数字，数据初始化值为0不限制，若修改必须为大于0小于365的正整数），如果超过这个周期登录系统时，则在登录时就会提醒修改密码对话框');
/*!40000 ALTER TABLE `sys_config` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_dept`
--

DROP TABLE IF EXISTS `sys_dept`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_dept` (
  `dept_id` bigint NOT NULL AUTO_INCREMENT COMMENT '部门id',
  `parent_id` bigint DEFAULT '0' COMMENT '父部门id',
  `ancestors` varchar(50) DEFAULT '' COMMENT '祖级列表',
  `dept_name` varchar(30) DEFAULT '' COMMENT '部门名称',
  `order_num` int DEFAULT '0' COMMENT '显示顺序',
  `leader` varchar(20) DEFAULT NULL COMMENT '负责人',
  `phone` varchar(11) DEFAULT NULL COMMENT '联系电话',
  `email` varchar(50) DEFAULT NULL COMMENT '邮箱',
  `status` char(1) DEFAULT '0' COMMENT '部门状态（0正常 1停用）',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`dept_id`)
) ENGINE=InnoDB AUTO_INCREMENT=200 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='部门表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_dept`
--

LOCK TABLES `sys_dept` WRITE;
/*!40000 ALTER TABLE `sys_dept` DISABLE KEYS */;
INSERT INTO `sys_dept` VALUES (100,0,'0','若依科技',0,'若依','15888888888','<EMAIL>','0','0','admin','2025-07-07 12:40:27','',NULL),(101,100,'0,100','深圳总公司',1,'若依','15888888888','<EMAIL>','0','0','admin','2025-07-07 12:40:27','',NULL),(102,100,'0,100','长沙分公司',2,'若依','15888888888','<EMAIL>','0','2','admin','2025-07-07 12:40:27','',NULL),(103,101,'0,100,101','研发部门',1,'','','','1','0','admin','2025-07-07 12:40:27','admin','2025-07-10 10:23:57'),(104,101,'0,100,101','市场部门',2,'若依','15888888888','<EMAIL>','0','2','admin','2025-07-07 12:40:27','',NULL),(105,101,'0,100,101','测试部门',3,'若依','15888888888','<EMAIL>','0','2','admin','2025-07-07 12:40:27','',NULL),(106,101,'0,100,101','财务部门',4,'若依','15888888888','<EMAIL>','0','2','admin','2025-07-07 12:40:27','',NULL),(107,101,'0,100,101','运维部门',5,'若依','15888888888','<EMAIL>','0','2','admin','2025-07-07 12:40:27','',NULL),(108,102,'0,100,102','市场部门',1,'若依','15888888888','<EMAIL>','0','2','admin','2025-07-07 12:40:27','',NULL),(109,102,'0,100,102','财务部门',2,'若依','15888888888','<EMAIL>','0','2','admin','2025-07-07 12:40:27','',NULL);
/*!40000 ALTER TABLE `sys_dept` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_dict_data`
--

DROP TABLE IF EXISTS `sys_dict_data`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_dict_data` (
  `dict_code` bigint NOT NULL AUTO_INCREMENT COMMENT '字典编码',
  `dict_sort` int DEFAULT '0' COMMENT '字典排序',
  `dict_label` varchar(100) DEFAULT '' COMMENT '字典标签',
  `dict_value` varchar(100) DEFAULT '' COMMENT '字典键值',
  `dict_type` varchar(100) DEFAULT '' COMMENT '字典类型',
  `css_class` varchar(100) DEFAULT NULL COMMENT '样式属性（其他样式扩展）',
  `list_class` varchar(100) DEFAULT NULL COMMENT '表格回显样式',
  `is_default` char(1) DEFAULT 'N' COMMENT '是否默认（Y是 N否）',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_code`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='字典数据表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_dict_data`
--

LOCK TABLES `sys_dict_data` WRITE;
/*!40000 ALTER TABLE `sys_dict_data` DISABLE KEYS */;
INSERT INTO `sys_dict_data` VALUES (1,1,'男','0','sys_user_sex','','','Y','0','admin','2025-07-07 12:40:28','',NULL,'性别男'),(2,2,'女','1','sys_user_sex','','','N','0','admin','2025-07-07 12:40:28','',NULL,'性别女'),(3,3,'未知','2','sys_user_sex','','','N','0','admin','2025-07-07 12:40:28','',NULL,'性别未知'),(4,1,'显示','0','sys_show_hide','','primary','Y','0','admin','2025-07-07 12:40:28','',NULL,'显示菜单'),(5,2,'隐藏','1','sys_show_hide','','danger','N','0','admin','2025-07-07 12:40:28','',NULL,'隐藏菜单'),(6,1,'正常','0','sys_normal_disable','','primary','Y','0','admin','2025-07-07 12:40:28','',NULL,'正常状态'),(7,2,'停用','1','sys_normal_disable','','danger','N','0','admin','2025-07-07 12:40:28','',NULL,'停用状态'),(8,1,'正常','0','sys_job_status','','primary','Y','0','admin','2025-07-07 12:40:28','',NULL,'正常状态'),(9,2,'暂停','1','sys_job_status','','danger','N','0','admin','2025-07-07 12:40:28','',NULL,'停用状态'),(10,1,'默认','DEFAULT','sys_job_group','','','Y','0','admin','2025-07-07 12:40:28','',NULL,'默认分组'),(11,2,'系统','SYSTEM','sys_job_group','','','N','0','admin','2025-07-07 12:40:28','',NULL,'系统分组'),(12,1,'是','Y','sys_yes_no','','primary','Y','0','admin','2025-07-07 12:40:28','',NULL,'系统默认是'),(13,2,'否','N','sys_yes_no','','danger','N','0','admin','2025-07-07 12:40:28','',NULL,'系统默认否'),(14,1,'通知','1','sys_notice_type','','warning','Y','0','admin','2025-07-07 12:40:28','',NULL,'通知'),(15,2,'公告','2','sys_notice_type','','success','N','0','admin','2025-07-07 12:40:28','',NULL,'公告'),(16,1,'正常','0','sys_notice_status','','primary','Y','0','admin','2025-07-07 12:40:28','',NULL,'正常状态'),(17,2,'关闭','1','sys_notice_status','','danger','N','0','admin','2025-07-07 12:40:28','',NULL,'关闭状态'),(18,99,'其他','0','sys_oper_type','','info','N','0','admin','2025-07-07 12:40:28','',NULL,'其他操作'),(19,1,'新增','1','sys_oper_type','','info','N','0','admin','2025-07-07 12:40:28','',NULL,'新增操作'),(20,2,'修改','2','sys_oper_type','','info','N','0','admin','2025-07-07 12:40:28','',NULL,'修改操作'),(21,3,'删除','3','sys_oper_type','','danger','N','0','admin','2025-07-07 12:40:28','',NULL,'删除操作'),(22,4,'授权','4','sys_oper_type','','primary','N','0','admin','2025-07-07 12:40:28','',NULL,'授权操作'),(23,5,'导出','5','sys_oper_type','','warning','N','0','admin','2025-07-07 12:40:28','',NULL,'导出操作'),(24,6,'导入','6','sys_oper_type','','warning','N','0','admin','2025-07-07 12:40:28','',NULL,'导入操作'),(25,7,'强退','7','sys_oper_type','','danger','N','0','admin','2025-07-07 12:40:28','',NULL,'强退操作'),(26,8,'生成代码','8','sys_oper_type','','warning','N','0','admin','2025-07-07 12:40:28','',NULL,'生成操作'),(27,9,'清空数据','9','sys_oper_type','','danger','N','0','admin','2025-07-07 12:40:28','',NULL,'清空操作'),(28,1,'成功','0','sys_common_status','','primary','N','0','admin','2025-07-07 12:40:28','',NULL,'正常状态'),(29,2,'失败','1','sys_common_status','','danger','N','0','admin','2025-07-07 12:40:28','',NULL,'停用状态');
/*!40000 ALTER TABLE `sys_dict_data` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_dict_type`
--

DROP TABLE IF EXISTS `sys_dict_type`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_dict_type` (
  `dict_id` bigint NOT NULL AUTO_INCREMENT COMMENT '字典主键',
  `dict_name` varchar(100) DEFAULT '' COMMENT '字典名称',
  `dict_type` varchar(100) DEFAULT '' COMMENT '字典类型',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_id`),
  UNIQUE KEY `dict_type` (`dict_type`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='字典类型表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_dict_type`
--

LOCK TABLES `sys_dict_type` WRITE;
/*!40000 ALTER TABLE `sys_dict_type` DISABLE KEYS */;
INSERT INTO `sys_dict_type` VALUES (1,'用户性别','sys_user_sex','0','admin','2025-07-07 12:40:28','',NULL,'用户性别列表'),(2,'菜单状态','sys_show_hide','0','admin','2025-07-07 12:40:28','',NULL,'菜单状态列表'),(3,'系统开关','sys_normal_disable','0','admin','2025-07-07 12:40:28','',NULL,'系统开关列表'),(4,'任务状态','sys_job_status','0','admin','2025-07-07 12:40:28','',NULL,'任务状态列表'),(5,'任务分组','sys_job_group','0','admin','2025-07-07 12:40:28','',NULL,'任务分组列表'),(6,'系统是否','sys_yes_no','0','admin','2025-07-07 12:40:28','',NULL,'系统是否列表'),(7,'通知类型','sys_notice_type','0','admin','2025-07-07 12:40:28','',NULL,'通知类型列表'),(8,'通知状态','sys_notice_status','0','admin','2025-07-07 12:40:28','',NULL,'通知状态列表'),(9,'操作类型','sys_oper_type','0','admin','2025-07-07 12:40:28','',NULL,'操作类型列表'),(10,'系统状态','sys_common_status','0','admin','2025-07-07 12:40:28','',NULL,'登录状态列表');
/*!40000 ALTER TABLE `sys_dict_type` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_job`
--

DROP TABLE IF EXISTS `sys_job`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_job` (
  `job_id` bigint NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `job_name` varchar(64) NOT NULL DEFAULT '' COMMENT '任务名称',
  `job_group` varchar(64) NOT NULL DEFAULT 'DEFAULT' COMMENT '任务组名',
  `invoke_target` varchar(500) NOT NULL COMMENT '调用目标字符串',
  `cron_expression` varchar(255) DEFAULT '' COMMENT 'cron执行表达式',
  `misfire_policy` varchar(20) DEFAULT '3' COMMENT '计划执行错误策略（1立即执行 2执行一次 3放弃执行）',
  `concurrent` char(1) DEFAULT '1' COMMENT '是否并发执行（0允许 1禁止）',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1暂停）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT '' COMMENT '备注信息',
  PRIMARY KEY (`job_id`,`job_name`,`job_group`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='定时任务调度表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_job`
--

LOCK TABLES `sys_job` WRITE;
/*!40000 ALTER TABLE `sys_job` DISABLE KEYS */;
INSERT INTO `sys_job` VALUES (1,'系统默认（无参）','DEFAULT','ryTask.ryNoParams','0/10 * * * * ?','3','1','1','admin','2025-07-07 12:40:28','',NULL,''),(2,'系统默认（有参）','DEFAULT','ryTask.ryParams(\'ry\')','0/15 * * * * ?','3','1','1','admin','2025-07-07 12:40:28','',NULL,''),(3,'系统默认（多参）','DEFAULT','ryTask.ryMultipleParams(\'ry\', true, 2000L, 316.50D, 100)','0/20 * * * * ?','3','1','1','admin','2025-07-07 12:40:28','',NULL,'');
/*!40000 ALTER TABLE `sys_job` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_job_log`
--

DROP TABLE IF EXISTS `sys_job_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_job_log` (
  `job_log_id` bigint NOT NULL AUTO_INCREMENT COMMENT '任务日志ID',
  `job_name` varchar(64) NOT NULL COMMENT '任务名称',
  `job_group` varchar(64) NOT NULL COMMENT '任务组名',
  `invoke_target` varchar(500) NOT NULL COMMENT '调用目标字符串',
  `job_message` varchar(500) DEFAULT NULL COMMENT '日志信息',
  `status` char(1) DEFAULT '0' COMMENT '执行状态（0正常 1失败）',
  `exception_info` varchar(2000) DEFAULT '' COMMENT '异常信息',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`job_log_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='定时任务调度日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_job_log`
--

LOCK TABLES `sys_job_log` WRITE;
/*!40000 ALTER TABLE `sys_job_log` DISABLE KEYS */;
/*!40000 ALTER TABLE `sys_job_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_logininfor`
--

DROP TABLE IF EXISTS `sys_logininfor`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_logininfor` (
  `info_id` bigint NOT NULL AUTO_INCREMENT COMMENT '访问ID',
  `user_name` varchar(50) DEFAULT '' COMMENT '用户账号',
  `ipaddr` varchar(128) DEFAULT '' COMMENT '登录IP地址',
  `login_location` varchar(255) DEFAULT '' COMMENT '登录地点',
  `browser` varchar(50) DEFAULT '' COMMENT '浏览器类型',
  `os` varchar(50) DEFAULT '' COMMENT '操作系统',
  `status` char(1) DEFAULT '0' COMMENT '登录状态（0成功 1失败）',
  `msg` varchar(255) DEFAULT '' COMMENT '提示消息',
  `login_time` datetime DEFAULT NULL COMMENT '访问时间',
  PRIMARY KEY (`info_id`),
  KEY `idx_sys_logininfor_s` (`status`),
  KEY `idx_sys_logininfor_lt` (`login_time`)
) ENGINE=InnoDB AUTO_INCREMENT=157 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='系统访问记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_logininfor`
--

LOCK TABLES `sys_logininfor` WRITE;
/*!40000 ALTER TABLE `sys_logininfor` DISABLE KEYS */;
INSERT INTO `sys_logininfor` VALUES (100,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-07 12:49:28'),(101,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-07 12:55:36'),(102,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-07 12:56:22'),(103,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-07 12:57:07'),(104,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-07 12:57:25'),(105,'tuke','127.0.0.1','内网IP','Chrome 13','Windows 10','0','注册成功','2025-07-07 12:57:41'),(106,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','1','验证码已失效','2025-07-07 13:00:16'),(107,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','1','验证码错误','2025-07-07 13:00:17'),(108,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-07 13:00:22'),(109,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-07 13:01:22'),(110,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-07 13:01:28'),(111,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-07 13:01:58'),(112,'tuke','127.0.0.1','内网IP','Chrome 13','Windows 10','0','注册成功','2025-07-07 13:02:12'),(113,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-07 13:02:42'),(114,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-07 13:03:18'),(115,'tuke','127.0.0.1','内网IP','Chrome 13','Windows 10','0','注册成功','2025-07-07 13:04:06'),(116,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-07 13:04:34'),(117,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-07 13:05:22'),(118,'tuke','127.0.0.1','内网IP','Chrome 13','Windows 10','0','注册成功','2025-07-07 13:05:38'),(119,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-07 13:05:43'),(120,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-07 13:14:45'),(121,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-07 13:17:20'),(122,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-07 13:17:31'),(123,'tuke','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-07 13:39:05'),(124,'tuke','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-07 13:39:18'),(125,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-07 13:39:32'),(126,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','1','验证码错误','2025-07-07 15:56:55'),(127,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-07 15:57:05'),(128,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-07 15:57:25'),(129,'tuke','127.0.0.1','内网IP','Chrome 13','Windows 10','1','用户不存在/密码错误','2025-07-07 15:57:46'),(130,'tuke','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-07 15:57:56'),(131,'tuke','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-07 15:58:03'),(132,'tuke','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-07 15:58:50'),(133,'tuke','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-07 15:59:06'),(134,'tuke','127.0.0.1','内网IP','Chrome 13','Windows 10','1','验证码已失效','2025-07-07 16:17:05'),(135,'tuke','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-07 16:17:11'),(136,'tuke','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-07 16:20:28'),(137,'tuke','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-07 16:20:48'),(138,'tuke','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-09 12:11:14'),(139,'tuke','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-09 12:11:24'),(140,'tuke','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-09 12:58:54'),(141,'tuke','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-09 12:59:01'),(142,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-09 12:59:13'),(143,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','1','验证码已失效','2025-07-09 14:22:10'),(144,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-09 14:22:16'),(145,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-09 17:30:16'),(146,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-09 17:35:36'),(147,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-09 17:37:02'),(148,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-09 18:14:41'),(149,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-10 09:26:32'),(150,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-10 09:38:14'),(151,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-10 09:38:20'),(152,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-10 10:11:13'),(153,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-10 10:11:25'),(154,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-10 10:26:05'),(155,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-10 10:26:09'),(156,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-10 13:01:44');
/*!40000 ALTER TABLE `sys_logininfor` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_menu`
--

DROP TABLE IF EXISTS `sys_menu`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_menu` (
  `menu_id` bigint NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `menu_name` varchar(50) NOT NULL COMMENT '菜单名称',
  `parent_id` bigint DEFAULT '0' COMMENT '父菜单ID',
  `order_num` int DEFAULT '0' COMMENT '显示顺序',
  `path` varchar(200) DEFAULT '' COMMENT '路由地址',
  `component` varchar(255) DEFAULT NULL COMMENT '组件路径',
  `query` varchar(255) DEFAULT NULL COMMENT '路由参数',
  `route_name` varchar(50) DEFAULT '' COMMENT '路由名称',
  `is_frame` int DEFAULT '1' COMMENT '是否为外链（0是 1否）',
  `is_cache` int DEFAULT '0' COMMENT '是否缓存（0缓存 1不缓存）',
  `menu_type` char(1) DEFAULT '' COMMENT '菜单类型（M目录 C菜单 F按钮）',
  `visible` char(1) DEFAULT '0' COMMENT '菜单状态（0显示 1隐藏）',
  `status` char(1) DEFAULT '0' COMMENT '菜单状态（0正常 1停用）',
  `perms` varchar(100) DEFAULT NULL COMMENT '权限标识',
  `icon` varchar(100) DEFAULT '#' COMMENT '菜单图标',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`menu_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2013 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='菜单权限表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_menu`
--

LOCK TABLES `sys_menu` WRITE;
/*!40000 ALTER TABLE `sys_menu` DISABLE KEYS */;
INSERT INTO `sys_menu` VALUES (1,'系统管理',0,1,'system',NULL,'','',1,0,'M','0','0','','system','admin','2025-07-07 12:40:27','',NULL,'系统管理目录'),(2,'系统监控',0,2,'monitor',NULL,'','',1,0,'M','0','0','','monitor','admin','2025-07-07 12:40:27','',NULL,'系统监控目录'),(3,'系统工具',0,3,'tool',NULL,'','',1,0,'M','0','0','','tool','admin','2025-07-07 12:40:27','',NULL,'系统工具目录'),(100,'用户管理',1,1,'user','system/user/index','','',1,0,'C','0','0','system:user:list','user','admin','2025-07-07 12:40:27','',NULL,'用户管理菜单'),(101,'角色管理',1,2,'role','system/role/index','','',1,0,'C','0','0','system:role:list','peoples','admin','2025-07-07 12:40:27','',NULL,'角色管理菜单'),(102,'菜单管理',1,3,'menu','system/menu/index','','',1,0,'C','0','0','system:menu:list','tree-table','admin','2025-07-07 12:40:27','',NULL,'菜单管理菜单'),(105,'字典管理',1,6,'dict','system/dict/index','','',1,0,'C','0','0','system:dict:list','dict','admin','2025-07-07 12:40:27','',NULL,'字典管理菜单'),(106,'参数设置',1,7,'config','system/config/index','','',1,0,'C','0','0','system:config:list','edit','admin','2025-07-07 12:40:27','',NULL,'参数设置菜单'),(107,'通知公告',1,8,'notice','system/notice/index','','',1,0,'C','0','0','system:notice:list','message','admin','2025-07-07 12:40:27','',NULL,'通知公告菜单'),(108,'日志管理',1,9,'log','','','',1,0,'M','0','0','','log','admin','2025-07-07 12:40:27','',NULL,'日志管理菜单'),(109,'在线用户',2,1,'online','monitor/online/index','','',1,0,'C','0','0','monitor:online:list','online','admin','2025-07-07 12:40:27','',NULL,'在线用户菜单'),(110,'定时任务',2,2,'job','monitor/job/index','','',1,0,'C','0','0','monitor:job:list','job','admin','2025-07-07 12:40:27','',NULL,'定时任务菜单'),(111,'数据监控',2,3,'druid','monitor/druid/index','','',1,0,'C','0','0','monitor:druid:list','druid','admin','2025-07-07 12:40:27','',NULL,'数据监控菜单'),(112,'服务监控',2,4,'server','monitor/server/index','','',1,0,'C','0','0','monitor:server:list','server','admin','2025-07-07 12:40:27','',NULL,'服务监控菜单'),(113,'缓存监控',2,5,'cache','monitor/cache/index','','',1,0,'C','0','0','monitor:cache:list','redis','admin','2025-07-07 12:40:27','',NULL,'缓存监控菜单'),(114,'缓存列表',2,6,'cacheList','monitor/cache/list','','',1,0,'C','0','0','monitor:cache:list','redis-list','admin','2025-07-07 12:40:27','',NULL,'缓存列表菜单'),(115,'表单构建',3,1,'build','tool/build/index','','',1,0,'C','0','0','tool:build:list','build','admin','2025-07-07 12:40:27','',NULL,'表单构建菜单'),(116,'代码生成',3,2,'gen','tool/gen/index','','',1,0,'C','0','0','tool:gen:list','code','admin','2025-07-07 12:40:27','',NULL,'代码生成菜单'),(500,'操作日志',108,1,'operlog','monitor/operlog/index','','',1,0,'C','0','0','monitor:operlog:list','form','admin','2025-07-07 12:40:27','',NULL,'操作日志菜单'),(501,'登录日志',108,2,'logininfor','monitor/logininfor/index','','',1,0,'C','0','0','monitor:logininfor:list','logininfor','admin','2025-07-07 12:40:27','',NULL,'登录日志菜单'),(1000,'用户查询',100,1,'','','','',1,0,'F','0','0','system:user:query','#','admin','2025-07-07 12:40:27','',NULL,''),(1001,'用户新增',100,2,'','','','',1,0,'F','0','0','system:user:add','#','admin','2025-07-07 12:40:27','',NULL,''),(1002,'用户修改',100,3,'','','','',1,0,'F','0','0','system:user:edit','#','admin','2025-07-07 12:40:27','',NULL,''),(1003,'用户删除',100,4,'','','','',1,0,'F','0','0','system:user:remove','#','admin','2025-07-07 12:40:27','',NULL,''),(1004,'用户导出',100,5,'','','','',1,0,'F','0','0','system:user:export','#','admin','2025-07-07 12:40:27','',NULL,''),(1005,'用户导入',100,6,'','','','',1,0,'F','0','0','system:user:import','#','admin','2025-07-07 12:40:27','',NULL,''),(1006,'重置密码',100,7,'','','','',1,0,'F','0','0','system:user:resetPwd','#','admin','2025-07-07 12:40:27','',NULL,''),(1007,'角色查询',101,1,'','','','',1,0,'F','0','0','system:role:query','#','admin','2025-07-07 12:40:27','',NULL,''),(1008,'角色新增',101,2,'','','','',1,0,'F','0','0','system:role:add','#','admin','2025-07-07 12:40:27','',NULL,''),(1009,'角色修改',101,3,'','','','',1,0,'F','0','0','system:role:edit','#','admin','2025-07-07 12:40:27','',NULL,''),(1010,'角色删除',101,4,'','','','',1,0,'F','0','0','system:role:remove','#','admin','2025-07-07 12:40:27','',NULL,''),(1011,'角色导出',101,5,'','','','',1,0,'F','0','0','system:role:export','#','admin','2025-07-07 12:40:27','',NULL,''),(1012,'菜单查询',102,1,'','','','',1,0,'F','0','0','system:menu:query','#','admin','2025-07-07 12:40:27','',NULL,''),(1013,'菜单新增',102,2,'','','','',1,0,'F','0','0','system:menu:add','#','admin','2025-07-07 12:40:27','',NULL,''),(1014,'菜单修改',102,3,'','','','',1,0,'F','0','0','system:menu:edit','#','admin','2025-07-07 12:40:27','',NULL,''),(1015,'菜单删除',102,4,'','','','',1,0,'F','0','0','system:menu:remove','#','admin','2025-07-07 12:40:27','',NULL,''),(1025,'字典查询',105,1,'#','','','',1,0,'F','0','0','system:dict:query','#','admin','2025-07-07 12:40:27','',NULL,''),(1026,'字典新增',105,2,'#','','','',1,0,'F','0','0','system:dict:add','#','admin','2025-07-07 12:40:27','',NULL,''),(1027,'字典修改',105,3,'#','','','',1,0,'F','0','0','system:dict:edit','#','admin','2025-07-07 12:40:27','',NULL,''),(1028,'字典删除',105,4,'#','','','',1,0,'F','0','0','system:dict:remove','#','admin','2025-07-07 12:40:27','',NULL,''),(1029,'字典导出',105,5,'#','','','',1,0,'F','0','0','system:dict:export','#','admin','2025-07-07 12:40:27','',NULL,''),(1030,'参数查询',106,1,'#','','','',1,0,'F','0','0','system:config:query','#','admin','2025-07-07 12:40:27','',NULL,''),(1031,'参数新增',106,2,'#','','','',1,0,'F','0','0','system:config:add','#','admin','2025-07-07 12:40:27','',NULL,''),(1032,'参数修改',106,3,'#','','','',1,0,'F','0','0','system:config:edit','#','admin','2025-07-07 12:40:27','',NULL,''),(1033,'参数删除',106,4,'#','','','',1,0,'F','0','0','system:config:remove','#','admin','2025-07-07 12:40:27','',NULL,''),(1034,'参数导出',106,5,'#','','','',1,0,'F','0','0','system:config:export','#','admin','2025-07-07 12:40:27','',NULL,''),(1035,'公告查询',107,1,'#','','','',1,0,'F','0','0','system:notice:query','#','admin','2025-07-07 12:40:27','',NULL,''),(1036,'公告新增',107,2,'#','','','',1,0,'F','0','0','system:notice:add','#','admin','2025-07-07 12:40:27','',NULL,''),(1037,'公告修改',107,3,'#','','','',1,0,'F','0','0','system:notice:edit','#','admin','2025-07-07 12:40:27','',NULL,''),(1038,'公告删除',107,4,'#','','','',1,0,'F','0','0','system:notice:remove','#','admin','2025-07-07 12:40:27','',NULL,''),(1039,'操作查询',500,1,'#','','','',1,0,'F','0','0','monitor:operlog:query','#','admin','2025-07-07 12:40:27','',NULL,''),(1040,'操作删除',500,2,'#','','','',1,0,'F','0','0','monitor:operlog:remove','#','admin','2025-07-07 12:40:27','',NULL,''),(1041,'日志导出',500,3,'#','','','',1,0,'F','0','0','monitor:operlog:export','#','admin','2025-07-07 12:40:27','',NULL,''),(1042,'登录查询',501,1,'#','','','',1,0,'F','0','0','monitor:logininfor:query','#','admin','2025-07-07 12:40:27','',NULL,''),(1043,'登录删除',501,2,'#','','','',1,0,'F','0','0','monitor:logininfor:remove','#','admin','2025-07-07 12:40:27','',NULL,''),(1044,'日志导出',501,3,'#','','','',1,0,'F','0','0','monitor:logininfor:export','#','admin','2025-07-07 12:40:27','',NULL,''),(1045,'账户解锁',501,4,'#','','','',1,0,'F','0','0','monitor:logininfor:unlock','#','admin','2025-07-07 12:40:27','',NULL,''),(1046,'在线查询',109,1,'#','','','',1,0,'F','0','0','monitor:online:query','#','admin','2025-07-07 12:40:27','',NULL,''),(1047,'批量强退',109,2,'#','','','',1,0,'F','0','0','monitor:online:batchLogout','#','admin','2025-07-07 12:40:27','',NULL,''),(1048,'单条强退',109,3,'#','','','',1,0,'F','0','0','monitor:online:forceLogout','#','admin','2025-07-07 12:40:27','',NULL,''),(1049,'任务查询',110,1,'#','','','',1,0,'F','0','0','monitor:job:query','#','admin','2025-07-07 12:40:27','',NULL,''),(1050,'任务新增',110,2,'#','','','',1,0,'F','0','0','monitor:job:add','#','admin','2025-07-07 12:40:27','',NULL,''),(1051,'任务修改',110,3,'#','','','',1,0,'F','0','0','monitor:job:edit','#','admin','2025-07-07 12:40:27','',NULL,''),(1052,'任务删除',110,4,'#','','','',1,0,'F','0','0','monitor:job:remove','#','admin','2025-07-07 12:40:27','',NULL,''),(1053,'状态修改',110,5,'#','','','',1,0,'F','0','0','monitor:job:changeStatus','#','admin','2025-07-07 12:40:27','',NULL,''),(1054,'任务导出',110,6,'#','','','',1,0,'F','0','0','monitor:job:export','#','admin','2025-07-07 12:40:27','',NULL,''),(1055,'生成查询',116,1,'#','','','',1,0,'F','0','0','tool:gen:query','#','admin','2025-07-07 12:40:27','',NULL,''),(1056,'生成修改',116,2,'#','','','',1,0,'F','0','0','tool:gen:edit','#','admin','2025-07-07 12:40:27','',NULL,''),(1057,'生成删除',116,3,'#','','','',1,0,'F','0','0','tool:gen:remove','#','admin','2025-07-07 12:40:27','',NULL,''),(1058,'导入代码',116,4,'#','','','',1,0,'F','0','0','tool:gen:import','#','admin','2025-07-07 12:40:27','',NULL,''),(1059,'预览代码',116,5,'#','','','',1,0,'F','0','0','tool:gen:preview','#','admin','2025-07-07 12:40:27','',NULL,''),(1060,'生成代码',116,6,'#','','','',1,0,'F','0','0','tool:gen:code','#','admin','2025-07-07 12:40:27','',NULL,''),(2000,'智能问答',0,5,'ai/chat','ai/chat/index',NULL,'',1,0,'C','0','0','ai:chat:view','search','admin','2025-07-09 13:31:55','admin','2025-07-10 11:11:12',''),(2001,'ai助手',0,4,'ai',NULL,NULL,'',1,0,'M','0','0','','search','admin','2025-07-09 16:13:06','admin','2025-07-10 09:49:56','智能问答菜单'),(2002,'智能问答',2001,1,'chat','ai/chat/index',NULL,'',1,0,'C','0','0','ai:chat:view','message','admin','2025-07-09 16:13:06','',NULL,'智能问答页面'),(2003,'会话管理',2001,2,'session','ai/session/index',NULL,'',1,0,'C','0','0','ai:session:list','list','admin','2025-07-09 16:13:06','',NULL,'会话管理页面'),(2004,'发送消息',2002,1,'','',NULL,'',1,0,'F','0','0','ai:chat:send','#','admin','2025-07-09 16:13:06','',NULL,''),(2005,'查看历史',2002,2,'','',NULL,'',1,0,'F','0','0','ai:chat:history','#','admin','2025-07-09 16:13:06','admin','2025-07-10 10:16:05',''),(2006,'创建会话',2002,3,'','',NULL,'',1,0,'F','0','0','ai:chat:session','#','admin','2025-07-09 16:13:06','',NULL,''),(2007,'导出记录',2002,4,'','',NULL,'',1,0,'F','0','0','ai:chat:export','#','admin','2025-07-09 16:13:06','',NULL,''),(2008,'会话查询',2003,1,'','',NULL,'',1,0,'F','0','0','ai:session:query','#','admin','2025-07-09 16:13:06','',NULL,''),(2009,'会话新增',2003,2,'','',NULL,'',1,0,'F','0','0','ai:session:add','#','admin','2025-07-09 16:13:06','',NULL,''),(2010,'会话修改',2003,3,'','',NULL,'',1,0,'F','0','0','ai:session:edit','#','admin','2025-07-09 16:13:06','',NULL,''),(2011,'会话删除',2003,4,'','',NULL,'',1,0,'F','0','0','ai:session:remove','#','admin','2025-07-09 16:13:06','',NULL,''),(2012,'会话导出',2003,5,'','',NULL,'',1,0,'F','0','0','ai:session:export','#','admin','2025-07-09 16:13:06','',NULL,'');
/*!40000 ALTER TABLE `sys_menu` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_notice`
--

DROP TABLE IF EXISTS `sys_notice`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_notice` (
  `notice_id` int NOT NULL AUTO_INCREMENT COMMENT '公告ID',
  `notice_title` varchar(50) NOT NULL COMMENT '公告标题',
  `notice_type` char(1) NOT NULL COMMENT '公告类型（1通知 2公告）',
  `notice_content` longblob COMMENT '公告内容',
  `status` char(1) DEFAULT '0' COMMENT '公告状态（0正常 1关闭）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`notice_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='通知公告表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_notice`
--

LOCK TABLES `sys_notice` WRITE;
/*!40000 ALTER TABLE `sys_notice` DISABLE KEYS */;
/*!40000 ALTER TABLE `sys_notice` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_oper_log`
--

DROP TABLE IF EXISTS `sys_oper_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_oper_log` (
  `oper_id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志主键',
  `title` varchar(50) DEFAULT '' COMMENT '模块标题',
  `business_type` int DEFAULT '0' COMMENT '业务类型（0其它 1新增 2修改 3删除）',
  `method` varchar(200) DEFAULT '' COMMENT '方法名称',
  `request_method` varchar(10) DEFAULT '' COMMENT '请求方式',
  `operator_type` int DEFAULT '0' COMMENT '操作类别（0其它 1后台用户 2手机端用户）',
  `oper_name` varchar(50) DEFAULT '' COMMENT '操作人员',
  `dept_name` varchar(50) DEFAULT '' COMMENT '部门名称',
  `oper_url` varchar(255) DEFAULT '' COMMENT '请求URL',
  `oper_ip` varchar(128) DEFAULT '' COMMENT '主机地址',
  `oper_location` varchar(255) DEFAULT '' COMMENT '操作地点',
  `oper_param` varchar(2000) DEFAULT '' COMMENT '请求参数',
  `json_result` varchar(2000) DEFAULT '' COMMENT '返回参数',
  `status` int DEFAULT '0' COMMENT '操作状态（0正常 1异常）',
  `error_msg` varchar(2000) DEFAULT '' COMMENT '错误消息',
  `oper_time` datetime DEFAULT NULL COMMENT '操作时间',
  `cost_time` bigint DEFAULT '0' COMMENT '消耗时间',
  PRIMARY KEY (`oper_id`),
  KEY `idx_sys_oper_log_bt` (`business_type`),
  KEY `idx_sys_oper_log_s` (`status`),
  KEY `idx_sys_oper_log_ot` (`oper_time`)
) ENGINE=InnoDB AUTO_INCREMENT=204 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='操作日志记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_oper_log`
--

LOCK TABLES `sys_oper_log` WRITE;
/*!40000 ALTER TABLE `sys_oper_log` DISABLE KEYS */;
INSERT INTO `sys_oper_log` VALUES (100,'参数管理',2,'com.ruoyi.web.controller.system.SysConfigController.edit()','PUT',1,'admin','研发部门','/system/config','127.0.0.1','内网IP','{\"configId\":5,\"configKey\":\"sys.account.registerUser\",\"configName\":\"账号自助-是否开启用户注册功能\",\"configType\":\"Y\",\"configValue\":\"true\",\"createBy\":\"admin\",\"createTime\":\"2025-07-07 12:40:28\",\"params\":{},\"remark\":\"是否开启注册用户功能（true开启，false关闭）\",\"updateBy\":\"admin\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 12:57:22',21),(101,'用户管理',3,'com.ruoyi.web.controller.system.SysUserController.remove()','DELETE',1,'admin','研发部门','/system/user/100','127.0.0.1','内网IP','[100]','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 13:01:56',24),(102,'用户管理',3,'com.ruoyi.web.controller.system.SysUserController.remove()','DELETE',1,'admin','研发部门','/system/user/101','127.0.0.1','内网IP','[101]','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 13:03:13',12),(103,'用户管理',3,'com.ruoyi.web.controller.system.SysUserController.remove()','DELETE',1,'admin','研发部门','/system/user/102','127.0.0.1','内网IP','[102]','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 13:05:19',11),(104,'菜单管理',3,'com.ruoyi.web.controller.system.SysMenuController.remove()','DELETE',1,'admin','研发部门','/system/menu/4','127.0.0.1','内网IP','4','{\"msg\":\"菜单已分配,不允许删除\",\"code\":601}',0,NULL,'2025-07-07 13:40:17',11),(105,'菜单管理',3,'com.ruoyi.web.controller.system.SysMenuController.remove()','DELETE',1,'admin','研发部门','/system/menu/4','127.0.0.1','内网IP','4','{\"msg\":\"菜单已分配,不允许删除\",\"code\":601}',0,NULL,'2025-07-07 13:40:24',6),(106,'角色管理',2,'com.ruoyi.web.controller.system.SysRoleController.dataScope()','PUT',1,'admin','研发部门','/system/role/dataScope','127.0.0.1','内网IP','{\"admin\":false,\"createTime\":\"2025-07-07 12:40:27\",\"dataScope\":\"2\",\"delFlag\":\"0\",\"deptCheckStrictly\":false,\"deptIds\":[101,105],\"flag\":false,\"menuCheckStrictly\":true,\"params\":{},\"remark\":\"普通角色\",\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 13:40:39',16),(107,'角色管理',4,'com.ruoyi.web.controller.system.SysRoleController.cancelAuthUser()','PUT',1,'admin','研发部门','/system/role/authUser/cancel','127.0.0.1','内网IP','{\"roleId\":2,\"userId\":2}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 13:40:47',10),(108,'用户管理',3,'com.ruoyi.web.controller.system.SysUserController.remove()','DELETE',1,'admin','研发部门','/system/user/2','127.0.0.1','内网IP','[2]','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 13:40:51',12),(109,'菜单管理',3,'com.ruoyi.web.controller.system.SysMenuController.remove()','DELETE',1,'admin','研发部门','/system/menu/4','127.0.0.1','内网IP','4','{\"msg\":\"菜单已分配,不允许删除\",\"code\":601}',0,NULL,'2025-07-07 13:40:57',3),(110,'角色管理',3,'com.ruoyi.web.controller.system.SysRoleController.remove()','DELETE',1,'admin','研发部门','/system/role/2','127.0.0.1','内网IP','[2]','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 13:41:05',23),(111,'菜单管理',3,'com.ruoyi.web.controller.system.SysMenuController.remove()','DELETE',1,'admin','研发部门','/system/menu/4','127.0.0.1','内网IP','4','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 13:41:10',11),(112,'菜单管理',3,'com.ruoyi.web.controller.system.SysMenuController.remove()','DELETE',1,'admin','研发部门','/system/menu/1019','127.0.0.1','内网IP','1019','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 13:41:25',13),(113,'菜单管理',3,'com.ruoyi.web.controller.system.SysMenuController.remove()','DELETE',1,'admin','研发部门','/system/menu/1018','127.0.0.1','内网IP','1018','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 13:41:28',11),(114,'菜单管理',3,'com.ruoyi.web.controller.system.SysMenuController.remove()','DELETE',1,'admin','研发部门','/system/menu/1017','127.0.0.1','内网IP','1017','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 13:41:31',7),(115,'菜单管理',3,'com.ruoyi.web.controller.system.SysMenuController.remove()','DELETE',1,'admin','研发部门','/system/menu/1016','127.0.0.1','内网IP','1016','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 13:41:32',8),(116,'用户头像',2,'com.ruoyi.web.controller.system.SysProfileController.avatar()','POST',1,'tuke',NULL,'/system/user/profile/avatar','127.0.0.1','内网IP','','{\"msg\":\"操作成功\",\"imgUrl\":\"/profile/avatar/2025/07/07/71fa11da96bb4e20b42197ec5d198232.png\",\"code\":200}',0,NULL,'2025-07-07 16:17:48',50),(117,'菜单管理',1,'com.ruoyi.web.controller.system.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"ai/chat/index\",\"createBy\":\"admin\",\"icon\":\"search\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"智能问答\",\"menuType\":\"C\",\"orderNum\":4,\"params\":{},\"parentId\":0,\"path\":\"/ai/chat\",\"perms\":\"ai:chat:view\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-09 13:31:55',164),(118,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"ai/chat/index\",\"createTime\":\"2025-07-09 13:31:55\",\"icon\":\"search\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2000,\"menuName\":\"智能问答\",\"menuType\":\"C\",\"orderNum\":4,\"params\":{},\"parentId\":0,\"path\":\"ai/chat\",\"perms\":\"ai:chat:view\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-09 13:32:19',21),(119,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"ai/chat/index\",\"createTime\":\"2025-07-09 13:31:55\",\"icon\":\"search\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2000,\"menuName\":\"智能问答\",\"menuType\":\"C\",\"orderNum\":4,\"params\":{},\"parentId\":0,\"path\":\"/ai/chat\",\"perms\":\"ai:chat:view\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-09 13:32:34',89),(120,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"ai/chat/index\",\"createTime\":\"2025-07-09 13:31:55\",\"icon\":\"search\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2000,\"menuName\":\"智能问答\",\"menuType\":\"C\",\"orderNum\":4,\"params\":{},\"parentId\":0,\"path\":\"/ai/chat\",\"perms\":\"ai:chat:view\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-09 13:33:16',9),(121,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"ai/chat/index\",\"createTime\":\"2025-07-09 13:31:55\",\"icon\":\"search\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2000,\"menuName\":\"智能问答\",\"menuType\":\"C\",\"orderNum\":4,\"params\":{},\"parentId\":0,\"path\":\"ai/chat\",\"perms\":\"ai:chat:view\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-09 13:35:13',8),(122,'AI聊天',1,'com.ruoyi.web.controller.ai.AiChatController.createSession()','POST',1,'admin','研发部门','/ai/chat/session','127.0.0.1','内网IP','{\"model\":\"gpt-3.5-turbo\",\"title\":\"新对话\"}','{\"msg\":\"操作成功\",\"code\":200,\"data\":{\"createBy\":\"admin\",\"createTime\":\"2025-07-09 14:22:25\",\"lastActiveTime\":\"2025-07-09 14:22:25\",\"messageCount\":0,\"model\":\"gpt-3.5-turbo\",\"params\":{},\"sessionId\":\"0a2f7c6dc0d941c9985296c7395e1127\",\"status\":\"0\",\"title\":\"新对话\",\"userId\":1,\"userName\":\"admin\"}}',0,NULL,'2025-07-09 14:22:25',5),(123,'AI聊天',0,'com.ruoyi.web.controller.ai.AiChatController.sendMessage()','POST',1,'admin','研发部门','/ai/chat/send','127.0.0.1','内网IP','{\"message\":\"你是谁\",\"model\":\"gpt-3.5-turbo\",\"sessionId\":\"0a2f7c6dc0d941c9985296c7395e1127\",\"stream\":true,\"temperature\":null}',NULL,1,'会话不存在','2025-07-09 14:28:24',11),(124,'AI聊天',1,'com.ruoyi.web.controller.ai.AiChatController.createSession()','POST',1,'admin','研发部门','/ai/chat/session','127.0.0.1','内网IP','{\"model\":\"gpt-3.5-turbo\",\"title\":\"新对话\"}','{\"msg\":\"操作成功\",\"code\":200,\"data\":{\"createBy\":\"admin\",\"createTime\":\"2025-07-09 14:28:32\",\"lastActiveTime\":\"2025-07-09 14:28:32\",\"messageCount\":0,\"model\":\"gpt-3.5-turbo\",\"params\":{},\"sessionId\":\"e52093f638214112b66863ecf07dc6ef\",\"status\":\"0\",\"title\":\"新对话\",\"userId\":1,\"userName\":\"admin\"}}',0,NULL,'2025-07-09 14:28:32',4),(125,'AI聊天',1,'com.ruoyi.web.controller.ai.AiChatController.createSession()','POST',1,'admin','研发部门','/ai/chat/session','127.0.0.1','内网IP','{\"model\":\"gpt-3.5-turbo\",\"title\":\"新对话\"}','{\"msg\":\"操作成功\",\"code\":200,\"data\":{\"createBy\":\"admin\",\"createTime\":\"2025-07-09 14:28:42\",\"lastActiveTime\":\"2025-07-09 14:28:42\",\"messageCount\":0,\"model\":\"gpt-3.5-turbo\",\"params\":{},\"sessionId\":\"c644f2ad4334403899247d68e00912aa\",\"status\":\"0\",\"title\":\"新对话\",\"userId\":1,\"userName\":\"admin\"}}',0,NULL,'2025-07-09 14:28:42',1),(126,'AI聊天',0,'com.ruoyi.web.controller.ai.AiChatController.sendMessage()','POST',1,'admin','研发部门','/ai/chat/send','127.0.0.1','内网IP','{\"message\":\"你是谁\",\"model\":\"gpt-3.5-turbo\",\"sessionId\":\"c644f2ad4334403899247d68e00912aa\",\"stream\":true,\"temperature\":null}',NULL,1,'AI服务暂时不可用，请稍后重试','2025-07-09 14:28:43',183),(127,'AI聊天',0,'com.ruoyi.web.controller.ai.AiChatController.sendMessage()','POST',1,'admin','研发部门','/ai/chat/send','127.0.0.1','内网IP','{\"message\":\"你是谁\",\"model\":\"gpt-3.5-turbo\",\"sessionId\":\"c644f2ad4334403899247d68e00912aa\",\"stream\":true,\"temperature\":null}',NULL,1,'会话不存在','2025-07-09 14:29:22',7),(128,'AI聊天',1,'com.ruoyi.web.controller.ai.AiChatController.createSession()','POST',1,'admin','研发部门','/ai/chat/session','127.0.0.1','内网IP','{\"model\":\"gpt-3.5-turbo\",\"title\":\"新对话\"}','{\"msg\":\"操作成功\",\"code\":200,\"data\":{\"createBy\":\"admin\",\"createTime\":\"2025-07-09 14:34:02\",\"lastActiveTime\":\"2025-07-09 14:34:02\",\"messageCount\":0,\"model\":\"gpt-3.5-turbo\",\"params\":{},\"sessionId\":\"eb3f170315944332bbbcebab8ebb44b9\",\"status\":\"0\",\"title\":\"新对话\",\"userId\":1,\"userName\":\"admin\"}}',0,NULL,'2025-07-09 14:34:02',7),(129,'AI聊天',0,'com.ruoyi.web.controller.ai.AiChatController.sendMessage()','POST',1,'admin','研发部门','/ai/chat/send','127.0.0.1','内网IP','{\"message\":\"你是谁\",\"model\":\"gpt-3.5-turbo\",\"sessionId\":\"eb3f170315944332bbbcebab8ebb44b9\",\"stream\":true,\"temperature\":null}',NULL,1,'AI服务暂时不可用，请稍后重试','2025-07-09 14:34:02',125),(130,'AI聊天',0,'com.ruoyi.web.controller.ai.AiChatController.sendMessage()','POST',1,'admin','研发部门','/ai/chat/send','127.0.0.1','内网IP','{\"message\":\"你是谁\",\"model\":\"gpt-3.5-turbo\",\"sessionId\":\"eb3f170315944332bbbcebab8ebb44b9\",\"stream\":true,\"temperature\":null}',NULL,1,'AI服务暂时不可用，请稍后重试','2025-07-09 14:34:24',185),(131,'AI聊天',0,'com.ruoyi.web.controller.ai.AiChatController.sendMessage()','POST',1,'admin','研发部门','/ai/chat/send','127.0.0.1','内网IP','{\"message\":\"你是谁\",\"model\":\"gpt-3.5-turbo\",\"sessionId\":\"eb3f170315944332bbbcebab8ebb44b9\",\"stream\":true,\"temperature\":null}',NULL,1,'AI服务暂时不可用，请稍后重试','2025-07-09 14:39:33',6),(132,'AI聊天',0,'com.ruoyi.web.controller.ai.AiChatController.sendMessage()','POST',1,'admin','研发部门','/ai/chat/send','127.0.0.1','内网IP','{\"message\":\"你是谁\",\"model\":\"gpt-3.5-turbo\",\"sessionId\":\"eb3f170315944332bbbcebab8ebb44b9\",\"stream\":true,\"temperature\":null}',NULL,1,'AI服务暂时不可用，请稍后重试','2025-07-09 14:45:54',255),(133,'AI聊天',1,'com.ruoyi.web.controller.ai.AiChatController.createSession()','POST',1,'admin','研发部门','/ai/chat/session','127.0.0.1','内网IP','{\"model\":\"qwen-plus\",\"title\":\"新对话\"}','{\"msg\":\"操作成功\",\"code\":200,\"data\":{\"createBy\":\"admin\",\"createTime\":\"2025-07-09 17:30:23\",\"lastActiveTime\":\"2025-07-09 17:30:23\",\"messageCount\":0,\"model\":\"qwen-plus\",\"params\":{},\"sessionId\":\"2c513b2847c54eacba64b1857a2553a8\",\"status\":\"0\",\"title\":\"新对话\",\"userId\":1,\"userName\":\"admin\"}}',0,NULL,'2025-07-09 17:30:23',23),(134,'AI聊天',1,'com.ruoyi.web.controller.ai.AiChatController.createSession()','POST',1,'admin','研发部门','/ai/chat/session','127.0.0.1','内网IP','{\"model\":\"qwen-plus\",\"title\":\"新对话\"}','{\"msg\":\"操作成功\",\"code\":200,\"data\":{\"createBy\":\"admin\",\"createTime\":\"2025-07-09 17:30:31\",\"lastActiveTime\":\"2025-07-09 17:30:31\",\"messageCount\":0,\"model\":\"qwen-plus\",\"params\":{},\"sessionId\":\"7645f01dcf1443b6ab36868237af4b88\",\"status\":\"0\",\"title\":\"新对话\",\"userId\":1,\"userName\":\"admin\"}}',0,NULL,'2025-07-09 17:30:30',7),(135,'AI聊天',0,'com.ruoyi.web.controller.ai.AiChatController.sendMessage()','POST',1,'admin','研发部门','/ai/chat/send','127.0.0.1','内网IP','{\"message\":\"你是谁\",\"model\":\"qwen-plus\",\"sessionId\":\"7645f01dcf1443b6ab36868237af4b88\",\"stream\":false}','{\"msg\":\"发送消息失败: nested exception is org.apache.ibatis.binding.BindingException: Parameter \'sessionId\' not found. Available parameters are [arg1, arg0, param1, param2]\",\"code\":500}',0,NULL,'2025-07-09 17:30:30',5),(136,'AI聊天',0,'com.ruoyi.web.controller.ai.AiChatController.sendMessage()','POST',1,'admin','研发部门','/ai/chat/send','127.0.0.1','内网IP','{\"message\":\"你是谁\",\"model\":\"qwen-plus\",\"sessionId\":\"7645f01dcf1443b6ab36868237af4b88\",\"stream\":false}','{\"msg\":\"发送消息失败: nested exception is org.apache.ibatis.binding.BindingException: Parameter \'sessionId\' not found. Available parameters are [arg1, arg0, param1, param2]\",\"code\":500}',0,NULL,'2025-07-09 18:15:02',13),(137,'AI聊天',0,'com.ruoyi.web.controller.ai.AiChatController.sendMessage()','POST',1,'admin','研发部门','/ai/chat/send','127.0.0.1','内网IP','{\"message\":\"你是谁\",\"model\":\"qwen-plus\",\"sessionId\":\"7645f01dcf1443b6ab36868237af4b88\",\"stream\":false}','{\"msg\":\"操作成功\",\"code\":200,\"data\":{\"content\":\"你好！我是小若，一个智能助手。我可以帮助你回答问题、提供信息和完成各种任务。有什么我可以帮你的吗？\",\"finished\":true,\"messageId\":\"36e8996134174716a4e4be076224991c\",\"responseTime\":1584}}',0,NULL,'2025-07-10 09:39:09',1613),(138,'AI聊天',0,'com.ruoyi.web.controller.ai.AiChatController.sendMessage()','POST',1,'admin','研发部门','/ai/chat/send','127.0.0.1','内网IP','{\"message\":\"你知道东哥吗\",\"model\":\"qwen-plus\",\"sessionId\":\"7645f01dcf1443b6ab36868237af4b88\",\"stream\":false}','{\"msg\":\"操作成功\",\"code\":200,\"data\":{\"content\":\"你好，如果你是指京东集团创始人刘强东，我可以提供一些公开的信息。刘强东是中国著名的企业家，他创立了京东集团，这是一家知名的电子商务和科技公司。如果你有具体的问题或需要了解的内容，可以告诉我，我会尽力提供帮助。\",\"finished\":true,\"messageId\":\"cee6a1ab7cac486097cd643cc99dc30d\",\"responseTime\":3608}}',0,NULL,'2025-07-10 09:39:32',3623),(139,'AI聊天',0,'com.ruoyi.web.controller.ai.AiChatController.sendMessage()','POST',1,'admin','研发部门','/ai/chat/send','127.0.0.1','内网IP','{\"message\":\"你知道陈冠宏吗\",\"model\":\"qwen-plus\",\"sessionId\":\"7645f01dcf1443b6ab36868237af4b88\",\"stream\":false}','{\"msg\":\"操作成功\",\"code\":200,\"data\":{\"content\":\"我对这个名字没有具体的信息。如果你能提供更多背景或具体问题，我会尽力帮助你查找或解答。\",\"finished\":true,\"messageId\":\"4c774bb51fce43f5b995d8987a6167ac\",\"responseTime\":917}}',0,NULL,'2025-07-10 09:40:33',933),(140,'AI聊天',0,'com.ruoyi.web.controller.ai.AiChatController.sendMessage()','POST',1,'admin','研发部门','/ai/chat/send','127.0.0.1','内网IP','{\"message\":\"你是谁\",\"model\":\"qwen-plus\",\"sessionId\":\"7645f01dcf1443b6ab36868237af4b88\",\"stream\":false}','{\"msg\":\"操作成功\",\"code\":200,\"data\":{\"content\":\"你好！我是小若，一个智能助手。我可以帮助你回答问题、提供信息和完成各种任务。有什么我可以帮你的吗？\",\"finished\":true,\"messageId\":\"5e9f683ef9b943b7b5a3b5ff964a6b42\",\"responseTime\":1702}}',0,NULL,'2025-07-10 09:45:16',1732),(141,'菜单管理',3,'com.ruoyi.web.controller.system.SysMenuController.remove()','DELETE',1,'admin','研发部门','/system/menu/2000','127.0.0.1','内网IP','2000','{\"msg\":\"菜单已分配,不允许删除\",\"code\":601}',0,NULL,'2025-07-10 09:48:21',17),(142,'AI聊天',3,'com.ruoyi.web.controller.ai.AiChatController.clearHistory()','DELETE',1,'admin','研发部门','/ai/chat/clear/7645f01dcf1443b6ab36868237af4b88','127.0.0.1','内网IP','\"7645f01dcf1443b6ab36868237af4b88\"','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-10 09:48:56',21),(143,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createTime\":\"2025-07-09 16:13:06\",\"icon\":\"search\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2001,\"menuName\":\"ai助手\",\"menuType\":\"M\",\"orderNum\":4,\"params\":{},\"parentId\":0,\"path\":\"ai\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-10 09:49:56',13),(144,'AI聊天',0,'com.ruoyi.web.controller.ai.AiChatController.sendMessage()','POST',1,'admin','研发部门','/ai/chat/send','127.0.0.1','内网IP','{\"message\":\"你是谁\",\"model\":\"qwen-plus\",\"sessionId\":\"7645f01dcf1443b6ab36868237af4b88\",\"stream\":false}','{\"msg\":\"操作成功\",\"code\":200,\"data\":{\"content\":\"你好！我是小若，一个智能助手。我可以帮助你回答问题、提供信息和完成各种任务。有什么我可以帮你的吗？\",\"finished\":true,\"messageId\":\"6e0e5f794903443bb82dcab11dfd9b5d\",\"responseTime\":3183}}',0,NULL,'2025-07-10 09:55:39',3198),(145,'AI聊天',0,'com.ruoyi.web.controller.ai.AiChatController.sendMessage()','POST',1,'admin','研发部门','/ai/chat/send','127.0.0.1','内网IP','{\"message\":\"1+1=多少\",\"model\":\"qwen-plus\",\"sessionId\":\"7645f01dcf1443b6ab36868237af4b88\",\"stream\":false}','{\"msg\":\"操作成功\",\"code\":200,\"data\":{\"content\":\"1 + 1 等于 2。\",\"finished\":true,\"messageId\":\"5e182e3c0ffd41e0affc1fe1eb2c38a5\",\"responseTime\":686}}',0,NULL,'2025-07-10 09:57:38',697),(146,'AI聊天',0,'com.ruoyi.web.controller.ai.AiChatController.sendMessage()','POST',1,'admin','研发部门','/ai/chat/send','127.0.0.1','内网IP','{\"message\":\"西安大学是211吗\",\"model\":\"qwen-plus\",\"sessionId\":\"7645f01dcf1443b6ab36868237af4b88\",\"stream\":false}','{\"msg\":\"操作成功\",\"code\":200,\"data\":{\"content\":\"西安大学不是211工程院校。根据公开资料，西安大学是一所地方性本科院校，未被列入国家“211工程”重点建设高校名单。\\n\\n如果你有其他关于学校选择或报考方面的问题，也欢迎继续提问！\",\"finished\":true,\"messageId\":\"4e5f934689834cc5ba49ec407009aae8\",\"responseTime\":2131}}',0,NULL,'2025-07-10 10:10:34',2145),(147,'菜单管理',3,'com.ruoyi.web.controller.system.SysMenuController.remove()','DELETE',1,'admin','研发部门','/system/menu/2000','127.0.0.1','内网IP','2000','{\"msg\":\"菜单已分配,不允许删除\",\"code\":601}',0,NULL,'2025-07-10 10:15:47',5),(148,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"\",\"createTime\":\"2025-07-09 16:13:06\",\"icon\":\"#\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2005,\"menuName\":\"查看历史\",\"menuType\":\"F\",\"orderNum\":2,\"params\":{},\"parentId\":2002,\"path\":\"\",\"perms\":\"ai:chat:history\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-10 10:16:05',9),(149,'菜单管理',3,'com.ruoyi.web.controller.system.SysMenuController.remove()','DELETE',1,'admin','研发部门','/system/menu/2005','127.0.0.1','内网IP','2005','{\"msg\":\"菜单已分配,不允许删除\",\"code\":601}',0,NULL,'2025-07-10 10:16:10',3),(150,'用户管理',3,'com.ruoyi.web.controller.system.SysUserController.remove()','DELETE',1,'admin','研发部门','/system/user/103','127.0.0.1','内网IP','[103]','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-10 10:17:46',22),(151,'菜单管理',3,'com.ruoyi.web.controller.system.SysMenuController.remove()','DELETE',1,'admin','研发部门','/system/menu/117','127.0.0.1','内网IP','117','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-10 10:22:36',19),(152,'菜单管理',3,'com.ruoyi.web.controller.system.SysMenuController.remove()','DELETE',1,'admin','研发部门','/system/menu/2005','127.0.0.1','内网IP','2005','{\"msg\":\"菜单已分配,不允许删除\",\"code\":601}',0,NULL,'2025-07-10 10:22:55',5),(153,'角色管理',2,'com.ruoyi.web.controller.system.SysRoleController.changeStatus()','PUT',1,'admin','研发部门','/system/role/changeStatus','127.0.0.1','内网IP','{\"admin\":true,\"deptCheckStrictly\":false,\"flag\":false,\"menuCheckStrictly\":false,\"params\":{},\"roleId\":1,\"status\":\"1\"}',NULL,1,'不允许操作超级管理员角色','2025-07-10 10:23:05',0),(154,'用户管理',2,'com.ruoyi.web.controller.system.SysUserController.changeStatus()','PUT',1,'admin','研发部门','/system/user/changeStatus','127.0.0.1','内网IP','{\"admin\":true,\"params\":{},\"status\":\"1\",\"userId\":1}',NULL,1,'不允许操作超级管理员用户','2025-07-10 10:23:10',2),(155,'部门管理',3,'com.ruoyi.web.controller.system.SysDeptController.remove()','DELETE',1,'admin','研发部门','/system/dept/102','127.0.0.1','内网IP','102','{\"msg\":\"存在下级部门,不允许删除\",\"code\":601}',0,NULL,'2025-07-10 10:23:19',3),(156,'部门管理',3,'com.ruoyi.web.controller.system.SysDeptController.remove()','DELETE',1,'admin','研发部门','/system/dept/109','127.0.0.1','内网IP','109','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-10 10:23:21',10),(157,'部门管理',3,'com.ruoyi.web.controller.system.SysDeptController.remove()','DELETE',1,'admin','研发部门','/system/dept/108','127.0.0.1','内网IP','108','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-10 10:23:23',12),(158,'部门管理',3,'com.ruoyi.web.controller.system.SysDeptController.remove()','DELETE',1,'admin','研发部门','/system/dept/102','127.0.0.1','内网IP','102','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-10 10:23:24',11),(159,'部门管理',3,'com.ruoyi.web.controller.system.SysDeptController.remove()','DELETE',1,'admin','研发部门','/system/dept/107','127.0.0.1','内网IP','107','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-10 10:23:26',14),(160,'部门管理',3,'com.ruoyi.web.controller.system.SysDeptController.remove()','DELETE',1,'admin','研发部门','/system/dept/106','127.0.0.1','内网IP','106','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-10 10:23:27',12),(161,'部门管理',3,'com.ruoyi.web.controller.system.SysDeptController.remove()','DELETE',1,'admin','研发部门','/system/dept/105','127.0.0.1','内网IP','105','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-10 10:23:29',8),(162,'部门管理',3,'com.ruoyi.web.controller.system.SysDeptController.remove()','DELETE',1,'admin','研发部门','/system/dept/104','127.0.0.1','内网IP','104','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-10 10:23:30',13),(163,'部门管理',3,'com.ruoyi.web.controller.system.SysDeptController.remove()','DELETE',1,'admin','研发部门','/system/dept/103','127.0.0.1','内网IP','103','{\"msg\":\"部门存在用户,不允许删除\",\"code\":601}',0,NULL,'2025-07-10 10:23:31',7),(164,'部门管理',3,'com.ruoyi.web.controller.system.SysDeptController.remove()','DELETE',1,'admin','研发部门','/system/dept/103','127.0.0.1','内网IP','103','{\"msg\":\"部门存在用户,不允许删除\",\"code\":601}',0,NULL,'2025-07-10 10:23:33',3),(165,'部门管理',2,'com.ruoyi.web.controller.system.SysDeptController.edit()','PUT',1,'admin','研发部门','/system/dept','127.0.0.1','内网IP','{\"ancestors\":\"0,100,101\",\"children\":[],\"deptId\":103,\"deptName\":\"研发部门\",\"email\":\"\",\"leader\":\"\",\"orderNum\":1,\"params\":{},\"parentId\":101,\"parentName\":\"深圳总公司\",\"phone\":\"\",\"status\":\"1\",\"updateBy\":\"admin\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-10 10:23:57',16),(166,'部门管理',3,'com.ruoyi.web.controller.system.SysDeptController.remove()','DELETE',1,'admin','研发部门','/system/dept/103','127.0.0.1','内网IP','103','{\"msg\":\"部门存在用户,不允许删除\",\"code\":601}',0,NULL,'2025-07-10 10:23:59',6),(167,'部门管理',3,'com.ruoyi.web.controller.system.SysDeptController.remove()','DELETE',1,'admin','研发部门','/system/dept/103','127.0.0.1','内网IP','103','{\"msg\":\"部门存在用户,不允许删除\",\"code\":601}',0,NULL,'2025-07-10 10:24:04',4),(168,'部门管理',3,'com.ruoyi.web.controller.system.SysDeptController.remove()','DELETE',1,'admin','研发部门','/system/dept/101','127.0.0.1','内网IP','101','{\"msg\":\"存在下级部门,不允许删除\",\"code\":601}',0,NULL,'2025-07-10 10:24:09',3),(169,'部门管理',3,'com.ruoyi.web.controller.system.SysDeptController.remove()','DELETE',1,'admin','研发部门','/system/dept/103','127.0.0.1','内网IP','103','{\"msg\":\"部门存在用户,不允许删除\",\"code\":601}',0,NULL,'2025-07-10 10:24:11',5),(170,'AI聊天',1,'com.ruoyi.web.controller.ai.AiChatController.createSession()','POST',1,'admin','研发部门','/ai/chat/session','127.0.0.1','内网IP','{\"model\":\"qwen-plus\",\"title\":\"新对话\"}','{\"msg\":\"操作成功\",\"code\":200,\"data\":{\"createBy\":\"admin\",\"createTime\":\"2025-07-10 10:25:06\",\"lastActiveTime\":\"2025-07-10 10:25:06\",\"messageCount\":0,\"model\":\"qwen-plus\",\"params\":{},\"sessionId\":\"8e53b27bf5f2481a9dbe9532ede519c2\",\"status\":\"0\",\"title\":\"新对话\",\"userId\":1,\"userName\":\"admin\"}}',0,NULL,'2025-07-10 10:25:06',42),(171,'AI聊天',1,'com.ruoyi.web.controller.ai.AiChatController.createSession()','POST',1,'admin','研发部门','/ai/chat/session','127.0.0.1','内网IP','{\"model\":\"qwen-plus\",\"title\":\"新对话\"}','{\"msg\":\"操作成功\",\"code\":200,\"data\":{\"createBy\":\"admin\",\"createTime\":\"2025-07-10 10:25:11\",\"lastActiveTime\":\"2025-07-10 10:25:11\",\"messageCount\":0,\"model\":\"qwen-plus\",\"params\":{},\"sessionId\":\"d080302a84444abf96a99ee2d80bc6f9\",\"status\":\"0\",\"title\":\"新对话\",\"userId\":1,\"userName\":\"admin\"}}',0,NULL,'2025-07-10 10:25:11',16),(172,'AI聊天',3,'com.ruoyi.web.controller.ai.AiChatController.clearHistory()','DELETE',1,'admin','研发部门','/ai/chat/clear/7645f01dcf1443b6ab36868237af4b88','127.0.0.1','内网IP','\"7645f01dcf1443b6ab36868237af4b88\"','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-10 10:25:25',16),(173,'AI聊天',3,'com.ruoyi.web.controller.ai.AiChatController.clearHistory()','DELETE',1,'admin','研发部门','/ai/chat/clear/d080302a84444abf96a99ee2d80bc6f9','127.0.0.1','内网IP','\"d080302a84444abf96a99ee2d80bc6f9\"','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-10 10:26:21',16),(174,'AI聊天会话',3,'com.ruoyi.web.controller.ai.AiSessionController.remove()','DELETE',1,'admin','研发部门','/ai/session/2c513b2847c54eacba64b1857a2553a8','127.0.0.1','内网IP','[\"2c513b2847c54eacba64b1857a2553a8\"]','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-10 10:26:31',10),(175,'AI聊天会话',3,'com.ruoyi.web.controller.ai.AiSessionController.remove()','DELETE',1,'admin','研发部门','/ai/session/7645f01dcf1443b6ab36868237af4b88','127.0.0.1','内网IP','[\"7645f01dcf1443b6ab36868237af4b88\"]','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-10 10:26:39',4),(176,'AI聊天会话',3,'com.ruoyi.web.controller.ai.AiSessionController.remove()','DELETE',1,'admin','研发部门','/ai/session/8e53b27bf5f2481a9dbe9532ede519c2','127.0.0.1','内网IP','[\"8e53b27bf5f2481a9dbe9532ede519c2\"]','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-10 10:26:40',5),(177,'AI聊天会话',3,'com.ruoyi.web.controller.ai.AiSessionController.remove()','DELETE',1,'admin','研发部门','/ai/session/d080302a84444abf96a99ee2d80bc6f9','127.0.0.1','内网IP','[\"d080302a84444abf96a99ee2d80bc6f9\"]','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-10 10:26:42',8),(178,'AI聊天',1,'com.ruoyi.web.controller.ai.AiChatController.createSession()','POST',1,'admin','研发部门','/ai/chat/session','127.0.0.1','内网IP','{\"model\":\"qwen-plus\",\"title\":\"新对话\"}','{\"msg\":\"操作成功\",\"code\":200,\"data\":{\"createBy\":\"admin\",\"createTime\":\"2025-07-10 10:26:45\",\"lastActiveTime\":\"2025-07-10 10:26:45\",\"messageCount\":0,\"model\":\"qwen-plus\",\"params\":{},\"sessionId\":\"96fb5448991144e7adc580332db42523\",\"status\":\"0\",\"title\":\"新对话\",\"userId\":1,\"userName\":\"admin\"}}',0,NULL,'2025-07-10 10:26:45',12),(179,'AI聊天',0,'com.ruoyi.web.controller.ai.AiChatController.sendMessage()','POST',1,'admin','研发部门','/ai/chat/send','127.0.0.1','内网IP','{\"message\":\"你是谁\",\"model\":\"qwen-plus\",\"sessionId\":\"96fb5448991144e7adc580332db42523\",\"stream\":false}','{\"msg\":\"操作成功\",\"code\":200,\"data\":{\"content\":\"你好！我是小若，你的智能助手。我可以帮助你解答各种问题、提供信息和协助处理任务。有什么我可以帮你的吗？\",\"finished\":true,\"messageId\":\"ffefd57feb674f9fa392615c32674c65\",\"responseTime\":1877}}',0,NULL,'2025-07-10 11:07:23',1921),(180,'岗位管理',3,'com.ruoyi.web.controller.system.SysPostController.remove()','DELETE',1,'admin','研发部门','/system/post/4','127.0.0.1','内网IP','[4]','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-10 11:09:37',16),(181,'岗位管理',3,'com.ruoyi.web.controller.system.SysPostController.remove()','DELETE',1,'admin','研发部门','/system/post/3','127.0.0.1','内网IP','[3]','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-10 11:09:38',16),(182,'岗位管理',3,'com.ruoyi.web.controller.system.SysPostController.remove()','DELETE',1,'admin','研发部门','/system/post/2','127.0.0.1','内网IP','[2]','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-10 11:09:40',12),(183,'岗位管理',3,'com.ruoyi.web.controller.system.SysPostController.remove()','DELETE',1,'admin','研发部门','/system/post/1','127.0.0.1','内网IP','[1]',NULL,1,'董事长已分配,不能删除','2025-07-10 11:09:41',8),(184,'菜单管理',3,'com.ruoyi.web.controller.system.SysMenuController.remove()','DELETE',1,'admin','研发部门','/system/menu/1024','127.0.0.1','内网IP','1024','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-10 11:10:05',12),(185,'菜单管理',3,'com.ruoyi.web.controller.system.SysMenuController.remove()','DELETE',1,'admin','研发部门','/system/menu/1023','127.0.0.1','内网IP','1023','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-10 11:10:07',12),(186,'菜单管理',3,'com.ruoyi.web.controller.system.SysMenuController.remove()','DELETE',1,'admin','研发部门','/system/menu/1021','127.0.0.1','内网IP','1021','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-10 11:10:09',10),(187,'菜单管理',3,'com.ruoyi.web.controller.system.SysMenuController.remove()','DELETE',1,'admin','研发部门','/system/menu/1020','127.0.0.1','内网IP','1020','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-10 11:10:10',10),(188,'菜单管理',3,'com.ruoyi.web.controller.system.SysMenuController.remove()','DELETE',1,'admin','研发部门','/system/menu/1022','127.0.0.1','内网IP','1022','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-10 11:10:12',10),(189,'菜单管理',3,'com.ruoyi.web.controller.system.SysMenuController.remove()','DELETE',1,'admin','研发部门','/system/menu/104','127.0.0.1','内网IP','104','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-10 11:10:15',9),(190,'菜单管理',3,'com.ruoyi.web.controller.system.SysMenuController.remove()','DELETE',1,'admin','研发部门','/system/menu/103','127.0.0.1','内网IP','103','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-10 11:10:32',12),(191,'菜单管理',3,'com.ruoyi.web.controller.system.SysMenuController.remove()','DELETE',1,'admin','研发部门','/system/menu/2000','127.0.0.1','内网IP','2000','{\"msg\":\"菜单已分配,不允许删除\",\"code\":601}',0,NULL,'2025-07-10 11:11:02',9),(192,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"ai/chat/index\",\"createTime\":\"2025-07-09 13:31:55\",\"icon\":\"search\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2000,\"menuName\":\"智能问答\",\"menuType\":\"C\",\"orderNum\":5,\"params\":{},\"parentId\":0,\"path\":\"ai/chat\",\"perms\":\"ai:chat:view\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-10 11:11:12',21),(193,'通知公告',3,'com.ruoyi.web.controller.system.SysNoticeController.remove()','DELETE',1,'admin','研发部门','/system/notice/2','127.0.0.1','内网IP','[2]','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-10 11:12:16',8),(194,'通知公告',3,'com.ruoyi.web.controller.system.SysNoticeController.remove()','DELETE',1,'admin','研发部门','/system/notice/1','127.0.0.1','内网IP','[1]','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-10 11:12:17',7),(195,'AI聊天',0,'com.ruoyi.web.controller.ai.AiChatController.sendMessage()','POST',1,'admin','研发部门','/ai/chat/send','127.0.0.1','内网IP','{\"message\":\"你知道如何和你对话采用流式输出吗\",\"model\":\"qwen-plus\",\"sessionId\":\"96fb5448991144e7adc580332db42523\",\"stream\":false}','{\"msg\":\"操作成功\",\"code\":200,\"data\":{\"content\":\"当然知道！流式输出是指在对话中逐步生成回复内容，而不是一次性输出全部内容。如果你希望我使用流式输出来回应你的问题，请告诉我，我会尽量以这种方式进行交流。\\n\\n不过需要注意的是，目前你在网页端或应用端与我交互时，通常是以一次性输出的形式获得回复。流式输出更多是在特定场景下（如语音助手、实时对话等）体现的一种交互方式。\\n\\n如果你想测试或体验流式输出的效果，可以告诉我，我会尽可能模拟这种交互风格。还有其他需要了解的吗？\",\"finished\":true,\"messageId\":\"86f3c304124e4b77938f774d8fae2f43\",\"responseTime\":5171}}',0,NULL,'2025-07-10 11:15:12',5184),(196,'AI聊天',0,'com.ruoyi.web.controller.ai.AiChatController.sendMessage()','POST',1,'admin','研发部门','/ai/chat/send','127.0.0.1','内网IP','{\"message\":\"如何将mysql中的数据库导出来\",\"model\":\"qwen-plus\",\"sessionId\":\"96fb5448991144e7adc580332db42523\",\"stream\":false}','{\"msg\":\"操作成功\",\"code\":200,\"data\":{\"content\":\"你好！将 MySQL 中的数据库导出，常用的方法有以下几种：\\n\\n### 1. 使用 `mysqldump` 命令行工具（推荐）\\n这是最常用、最简单的方式。命令如下：\\n\\n```bash\\nmysqldump -u 用户名 -p 数据库名 > 导出的文件名.sql\\n```\\n\\n例如：\\n```bash\\nmysqldump -u root -p mydatabase > mydatabase_backup.sql\\n```\\n\\n执行后会提示你输入密码，正确输入后即可导出。\\n\\n#### 导出所有数据库：\\n```bash\\nmysqldump -u root -p --all-databases > all_databases_backup.sql\\n```\\n\\n---\\n\\n### 2. 使用 MySQL 客户端工具（如 phpMyAdmin、Navicat 等）\\n\\n- **phpMyAdmin**：登录后选择数据库 → 点击“导出” → 选择“快速”或“自定义”方式导出。\\n- **Navicat / DBeaver**：右键数据库或表，选择“导出数据”或类似选项，按向导操作即可。\\n\\n---\\n\\n### 3. 使用 SQL 命令导出部分数据（如单个表或查询结果）\\n\\n你可以使用 `SELECT INTO OUTFILE` 来导出某个查询结果到文件中：\\n\\n```sql\\nSELECT * FROM mytable\\nINTO OUTFILE \'/tmp/mytable_data.csv\'\\nFIELDS TERMINATED BY \',\' \\nENCLOSED BY \'\\\"\'\\nLINES TERMINATED BY \'\\\\n\';\\n```\\n\\n注意：这种方式要求你有写入服务器文件系统的权限。\\n\\n---\\n\\n如果你告诉我你使用的具体环境（比如操作系统、是否用图形工具等），我可以给你更具体的指导哦 ?\",\"finished\":true,\"messageId\":\"6de6122b855e46c8959bf37239f9c06a\",\"responseTime\":14828}}',0,NULL,'2025-07-10 11:17:56',14862),(197,'AI聊天',0,'com.ruoyi.web.controller.ai.AiChatController.sendMessage()','POST',1,'admin','研发部门','/ai/chat/send','127.0.0.1','内网IP','{\"message\":\"如何将mysql中的数据库导出来\",\"model\":\"qwen-plus\",\"sessionId\":\"96fb5448991144e7adc580332db42523\",\"stream\":false}','{\"msg\":\"操作成功\",\"code\":200,\"data\":{\"content\":\"你好！导出 MySQL 数据库最常用的方法是使用命令行工具 `mysqldump`，以下是具体操作步骤：\\n\\n---\\n\\n### ✅ 使用 `mysqldump` 导出数据库\\n\\n#### 基本命令格式：\\n```bash\\nmysqldump -u 用户名 -p 数据库名 > 文件名.sql\\n```\\n\\n#### 示例：\\n```bash\\nmysqldump -u root -p mydatabase > mydatabase_backup.sql\\n```\\n\\n执行后会提示你输入密码，正确输入后就会将 `mydatabase` 导出为 `mydatabase_backup.sql` 文件。\\n\\n---\\n\\n### ? 导出所有数据库：\\n如果你想一次性导出所有数据库，可以使用：\\n```bash\\nmysqldump -u root -p --all-databases > all_databases_backup.sql\\n```\\n\\n---\\n\\n### ? 其他可选参数：\\n\\n- **只导出结构（不带数据）**：\\n  ```bash\\n  mysqldump -u root -p --no-data mydatabase > mydatabase_structure.sql\\n  ```\\n\\n- **压缩导出（节省空间）**：\\n  ```bash\\n  mysqldump -u root -p mydatabase | gzip > mydatabase_backup.sql.gz\\n  ```\\n\\n---\\n\\n### ?️ 如果你在用图形化工具（如 phpMyAdmin、Navicat 等）：\\n\\n- 打开工具 → 选择目标数据库 → 点击“导出”按钮 → 选择导出格式（通常选 SQL）→ 下载文件即可。\\n\\n---\\n\\n如果你告诉我你是用的 Windows 还是 Linux，或者是否使用图形界面，我可以提供更贴合你环境的操作说明哦 ?\",\"finished\":true,\"messageId\":\"57439b60f26b40e6be2e2ea303c5c574\",\"responseTime\":16391}}',0,NULL,'2025-07-10 11:18:23',16401),(198,'AI聊天',0,'com.ruoyi.web.controller.ai.AiChatController.sendMessage()','POST',1,'admin','研发部门','/ai/chat/send','127.0.0.1','内网IP','{\"message\":\"如何将mysql中的数据库导出来\",\"model\":\"qwen-plus\",\"sessionId\":\"96fb5448991144e7adc580332db42523\",\"stream\":false}','{\"msg\":\"操作成功\",\"code\":200,\"data\":{\"content\":\"在 MySQL 中，导出数据库最常用的方法是使用 `mysqldump` 工具。以下是具体操作步骤：\\n\\n### 一、使用 `mysqldump` 导出数据库\\n\\n#### 基本命令格式：\\n```bash\\nmysqldump -u [用户名] -p [数据库名] > [导出的文件名.sql]\\n```\\n\\n#### 示例：\\n```bash\\nmysqldump -u root -p mydatabase > mydatabase_backup.sql\\n```\\n执行后会提示你输入密码，输入正确密码后即可开始导出。\\n\\n---\\n\\n### 二、导出所有数据库\\n如果你想导出 MySQL 中所有的数据库，可以加上 `--all-databases` 参数：\\n```bash\\nmysqldump -u root -p --all-databases > all_databases_backup.sql\\n```\\n\\n---\\n\\n### 三、附加常用选项\\n- `--single-transaction`：用于一致性备份（适用于 InnoDB）\\n- `--lock-tables=false`：不锁表（适合导出时不影响正在运行的服务）\\n- `--routines`：导出存储过程和函数\\n- `--events`：导出事件调度器\\n\\n例如：\\n```bash\\nmysqldump -u root -p --single-transaction mydatabase > mydatabase_backup.sql\\n```\\n\\n---\\n\\n### 四、导出文件位置\\n导出的 `.sql` 文件默认保存在当前终端所在的目录，你可以用 `pwd` 查看当前路径，或者指定完整路径保存，例如：\\n```bash\\nmysqldump -u root -p mydatabase > /home/<USER>/backups/mydatabase_backup.sql\\n```\\n\\n---\\n\\n如果你需要导入数据库，也可以告诉我，我可以提供导入方法 ?\",\"finished\":true,\"messageId\":\"5119b0dc20c24ebdae6b28314860a58f\",\"responseTime\":14550}}',0,NULL,'2025-07-10 11:18:55',14594),(199,'AI聊天',0,'com.ruoyi.web.controller.ai.AiChatController.sendMessage()','POST',1,'admin','研发部门','/ai/chat/send','127.0.0.1','内网IP','{\"message\":\"如何将mysql中的数据库导出来\",\"model\":\"qwen-plus\",\"sessionId\":\"96fb5448991144e7adc580332db42523\",\"stream\":false}','{\"msg\":\"操作成功\",\"code\":200,\"data\":{\"content\":\"要将 MySQL 中的数据库导出，最常用的方法是使用 `mysqldump` 工具。以下是简洁明了的操作说明：\\n\\n---\\n\\n### ✅ 基本命令格式：\\n```bash\\nmysqldump -u [用户名] -p [数据库名] > [保存路径/文件名.sql]\\n```\\n\\n#### 示例：\\n```bash\\nmysqldump -u root -p mydatabase > mydatabase_backup.sql\\n```\\n执行后输入密码即可导出。\\n\\n---\\n\\n### ? 导出所有数据库：\\n```bash\\nmysqldump -u root -p --all-databases > all_databases_backup.sql\\n```\\n\\n---\\n\\n### ? 常用可选参数：\\n- `--single-transaction`：保证数据一致性（适合 InnoDB）\\n- `--routines`：导出存储过程和函数\\n- `--events`：导出事件\\n- `--no-data`：只导出结构不导出数据\\n\\n示例：\\n```bash\\nmysqldump -u root -p --single-transaction --routines mydatabase > mydatabase_backup.sql\\n```\\n\\n---\\n\\n### ? 文件保存位置：\\n默认保存在当前目录，你可以使用绝对路径指定其他位置，例如：\\n```bash\\nmysqldump -u root -p mydatabase > /home/<USER>/backup/mydatabase.sql\\n```\\n\\n如需进一步帮助，比如导入数据库或压缩备份，也欢迎继续提问 ?\",\"finished\":true,\"messageId\":\"383f4b4fded347a49fdc8deb09697177\",\"responseTime\":11460}}',0,NULL,'2025-07-10 11:22:02',11473),(200,'AI聊天',0,'com.ruoyi.web.controller.ai.AiChatController.sendMessage()','POST',1,'admin','研发部门','/ai/chat/send','127.0.0.1','内网IP','{\"message\":\"如何将mysql中的数据库导出来\",\"model\":\"qwen-plus\",\"sessionId\":\"96fb5448991144e7adc580332db42523\",\"stream\":false}','{\"msg\":\"操作成功\",\"code\":200,\"data\":{\"content\":\"在 MySQL 中，导出数据库的常用方法是使用 `mysqldump` 工具。这是一个命令行工具，可以将数据库结构和数据导出为 SQL 文件。以下是基本的导出步骤：\\n\\n### 1. 导出整个数据库\\n\\n```bash\\nmysqldump -u 用户名 -p 数据库名 > 导出文件路径.sql\\n```\\n\\n例如：\\n\\n```bash\\nmysqldump -u root -p mydatabase > /path/to/backup/mydatabase.sql\\n```\\n\\n系统会提示你输入密码。\\n\\n### 2. 导出多个数据库\\n\\n```bash\\nmysqldump -u 用户名 -p --databases 数据库名1 数据库名2 > 导出文件路径.sql\\n```\\n\\n### 3. 导出所有数据库\\n\\n```bash\\nmysqldump -u 用户名 -p --all-databases > 导出文件路径.sql\\n```\\n\\n### 4. 常用选项说明：\\n\\n- `-u`：指定数据库用户名\\n- `-p`：表示需要输入密码（回车后会提示）\\n- `--databases`：导出多个数据库\\n- `--all-databases`：导出所有数据库\\n- `>`：输出到文件\\n\\n---\\n\\n✅ **提示：**\\n- 如果你在远程服务器上操作，请确保你有访问数据库的权限。\\n- 导出的 `.sql` 文件可以用文本编辑器打开查看，也可以导入到另一个 MySQL 实例中。\\n\\n如果你需要导入这个备份文件，可以使用 `mysql` 命令进行导入。\\n\\n需要我告诉你如何导入吗？\",\"finished\":true,\"messageId\":\"57bf7809534743728a1eaea6b9fd4f87\",\"responseTime\":15027}}',0,NULL,'2025-07-10 13:02:25',15056),(201,'AI聊天',0,'com.ruoyi.web.controller.ai.AiChatController.sendMessage()','POST',1,'admin','研发部门','/ai/chat/send','127.0.0.1','内网IP','{\"message\":\"如何将mysql中的数据库导出来\",\"model\":\"qwen-plus\",\"sessionId\":\"96fb5448991144e7adc580332db42523\",\"stream\":false}','{\"msg\":\"操作成功\",\"code\":200,\"data\":{\"content\":\"你好！在 MySQL 中导出数据库，最常用的方法是使用 `mysqldump` 工具。以下是基本的导出步骤：\\n\\n### 1. 打开终端或命令行工具\\n\\n### 2. 使用 `mysqldump` 导出数据库\\n\\n语法如下：\\n\\n```bash\\nmysqldump -u [用户名] -p [数据库名] > [导出的文件名.sql]\\n```\\n\\n示例：\\n\\n```bash\\nmysqldump -u root -p mydatabase > backup.sql\\n```\\n\\n系统会提示你输入密码，输入正确密码后就开始导出了。\\n\\n---\\n\\n### 常用选项说明：\\n\\n- `-u`：指定数据库用户名  \\n- `-p`：表示需要输入密码  \\n- `>`：将输出结果保存到文件  \\n\\n---\\n\\n### 如果你想导出所有数据库：\\n\\n```bash\\nmysqldump -u root -p --all-databases > all_backup.sql\\n```\\n\\n---\\n\\n导出完成后，`.sql` 文件就可以用于备份或导入到其他 MySQL 环境中了。\\n\\n如果你有更多具体需求（比如只导出结构、不导出数据等），也可以告诉我，我可以提供更详细的帮助 ?\",\"finished\":true,\"messageId\":\"2598f40c143342bfa33be0eb9b8a4e60\",\"responseTime\":8352}}',0,NULL,'2025-07-10 13:07:47',8377),(202,'AI聊天',0,'com.ruoyi.web.controller.ai.AiChatController.sendMessage()','POST',1,'admin','研发部门','/ai/chat/send','127.0.0.1','内网IP','{\"message\":\"如何将mysql中的数据库导出来\",\"model\":\"qwen-plus\",\"sessionId\":\"96fb5448991144e7adc580332db42523\",\"stream\":false}','{\"msg\":\"操作成功\",\"code\":200,\"data\":{\"content\":\"在 MySQL 中导出数据库，最常用的方法是使用 `mysqldump` 工具。以下是几种常见方式：\\n\\n### 1. 导出整个数据库\\n```bash\\nmysqldump -u 用户名 -p 数据库名 > 导出文件路径.sql\\n```\\n例如：\\n```bash\\nmysqldump -u root -p mydatabase > /path/to/backup.sql\\n```\\n\\n系统会提示你输入密码。\\n\\n---\\n\\n### 2. 导出多个数据库\\n```bash\\nmysqldump -u 用户名 -p --databases 数据库名1 数据库名2 > 导出文件路径.sql\\n```\\n\\n---\\n\\n### 3. 导出所有数据库\\n```bash\\nmysqldump -u 用户名 -p --all-databases > 导出文件路径.sql\\n```\\n\\n---\\n\\n### 4. 只导出表结构（不包含数据）\\n添加 `-d` 参数：\\n```bash\\nmysqldump -u root -p -d mydatabase > backup_structure.sql\\n```\\n\\n---\\n\\n### 5. 只导出数据（不包含结构）\\n添加 `-t` 参数：\\n```bash\\nmysqldump -u root -p -t mydatabase > backup_data.sql\\n```\\n\\n---\\n\\n### 小贴士：\\n- 如果你在远程服务器上操作，可以将 `.sql` 文件导出到你喜欢的目录。\\n- 使用 `source 备份文件.sql` 可以导入数据库。\\n- 定期备份建议写成脚本 + crontab 自动执行。\\n\\n如果你有具体需求（比如只导出某些表、压缩保存等），欢迎继续问我 ?\",\"finished\":true,\"messageId\":\"6062b93cee034e7a8ea351ae636c3045\",\"responseTime\":13823}}',0,NULL,'2025-07-10 13:11:37',13858),(203,'AI聊天',0,'com.ruoyi.web.controller.ai.AiChatController.sendMessage()','POST',1,'admin','研发部门','/ai/chat/send','127.0.0.1','内网IP','{\"message\":\"Usage: mysqldump [OPTIONS] database [tables]\\nOR     mysqldump [OPTIONS] --databases [OPTIONS] DB1 [DB2 DB3...]\\nOR     mysqldump [OPTIONS] --all-databases [OPTIONS]\\nFor more options, use mysqldump --help\\n\",\"model\":\"qwen-plus\",\"sessionId\":\"96fb5448991144e7adc580332db42523\",\"stream\":false}','{\"msg\":\"操作成功\",\"code\":200,\"data\":{\"content\":\"你看到的是 `mysqldump` 命令的基本用法说明。它提示了三种常见的使用方式：\\n\\n---\\n\\n### ✅ 1. 导出单个数据库或其中的某些表：\\n```bash\\nmysqldump [OPTIONS] database [tables]\\n```\\n**示例：**\\n```bash\\nmysqldump -u root -p mydb > mydb_backup.sql          # 导出整个数据库\\nmysqldump -u root -p mydb mytable > mytable.sql      # 只导出某个表\\n```\\n\\n---\\n\\n### ✅ 2. 导出多个指定的数据库：\\n```bash\\nmysqldump [OPTIONS] --databases DB1 [DB2 DB3...]\\n```\\n**示例：**\\n```bash\\nmysqldump -u root -p --databases db1 db2 > backup.sql\\n```\\n\\n---\\n\\n### ✅ 3. 导出所有数据库：\\n```bash\\nmysqldump [OPTIONS] --all-databases [OPTIONS]\\n```\\n**示例：**\\n```bash\\nmysqldump -u root -p --all-databases > full_backup.sql\\n```\\n\\n---\\n\\n? **常用选项说明：**\\n\\n| 参数 | 含义 |\\n|------|------|\\n| `-u` | 指定用户名（如：-u root）|\\n| `-p` | 表示需要输入密码（运行后会提示输入）|\\n| `-d` | 只导出结构，不导数据 |\\n| `-t` | 只导出数据，不导结构 |\\n| `--single-transaction` | 对 InnoDB 表进行一致性备份，不锁表 |\\n\\n---\\n\\n? 如果你想查看完整参数列表，可以运行：\\n```bash\\nmysqldump --help\\n```\\n\\n如果你有具体目标（例如定时备份、压缩导出等），我可以帮你写命令或脚本 ?\",\"finished\":true,\"messageId\":\"9707e91871de4e469c7734c2d2cd6508\",\"responseTime\":14480}}',0,NULL,'2025-07-10 13:16:59',14505);
/*!40000 ALTER TABLE `sys_oper_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_post`
--

DROP TABLE IF EXISTS `sys_post`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_post` (
  `post_id` bigint NOT NULL AUTO_INCREMENT COMMENT '岗位ID',
  `post_code` varchar(64) NOT NULL COMMENT '岗位编码',
  `post_name` varchar(50) NOT NULL COMMENT '岗位名称',
  `post_sort` int NOT NULL COMMENT '显示顺序',
  `status` char(1) NOT NULL COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`post_id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='岗位信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_post`
--

LOCK TABLES `sys_post` WRITE;
/*!40000 ALTER TABLE `sys_post` DISABLE KEYS */;
INSERT INTO `sys_post` VALUES (1,'ceo','董事长',1,'0','admin','2025-07-07 12:40:27','',NULL,'');
/*!40000 ALTER TABLE `sys_post` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_role`
--

DROP TABLE IF EXISTS `sys_role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_role` (
  `role_id` bigint NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_name` varchar(30) NOT NULL COMMENT '角色名称',
  `role_key` varchar(100) NOT NULL COMMENT '角色权限字符串',
  `role_sort` int NOT NULL COMMENT '显示顺序',
  `data_scope` char(1) DEFAULT '1' COMMENT '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）',
  `menu_check_strictly` tinyint(1) DEFAULT '1' COMMENT '菜单树选择项是否关联显示',
  `dept_check_strictly` tinyint(1) DEFAULT '1' COMMENT '部门树选择项是否关联显示',
  `status` char(1) NOT NULL COMMENT '角色状态（0正常 1停用）',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`role_id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='角色信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_role`
--

LOCK TABLES `sys_role` WRITE;
/*!40000 ALTER TABLE `sys_role` DISABLE KEYS */;
INSERT INTO `sys_role` VALUES (1,'超级管理员','admin',1,'1',1,1,'0','0','admin','2025-07-07 12:40:27','',NULL,'超级管理员'),(2,'普通角色','common',2,'2',1,0,'0','2','admin','2025-07-07 12:40:27','','2025-07-07 13:40:39','普通角色');
/*!40000 ALTER TABLE `sys_role` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_role_dept`
--

DROP TABLE IF EXISTS `sys_role_dept`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_role_dept` (
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `dept_id` bigint NOT NULL COMMENT '部门ID',
  PRIMARY KEY (`role_id`,`dept_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='角色和部门关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_role_dept`
--

LOCK TABLES `sys_role_dept` WRITE;
/*!40000 ALTER TABLE `sys_role_dept` DISABLE KEYS */;
/*!40000 ALTER TABLE `sys_role_dept` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_role_menu`
--

DROP TABLE IF EXISTS `sys_role_menu`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_role_menu` (
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `menu_id` bigint NOT NULL COMMENT '菜单ID',
  PRIMARY KEY (`role_id`,`menu_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='角色和菜单关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_role_menu`
--

LOCK TABLES `sys_role_menu` WRITE;
/*!40000 ALTER TABLE `sys_role_menu` DISABLE KEYS */;
INSERT INTO `sys_role_menu` VALUES (1,2000),(1,2002),(1,2003),(1,2004),(1,2005),(1,2006),(1,2007),(1,2008),(1,2009),(1,2010),(1,2011),(1,2012);
/*!40000 ALTER TABLE `sys_role_menu` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_user`
--

DROP TABLE IF EXISTS `sys_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_user` (
  `user_id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `dept_id` bigint DEFAULT NULL COMMENT '部门ID',
  `user_name` varchar(30) NOT NULL COMMENT '用户账号',
  `nick_name` varchar(30) NOT NULL COMMENT '用户昵称',
  `user_type` varchar(2) DEFAULT '00' COMMENT '用户类型（00系统用户）',
  `email` varchar(50) DEFAULT '' COMMENT '用户邮箱',
  `phonenumber` varchar(11) DEFAULT '' COMMENT '手机号码',
  `sex` char(1) DEFAULT '0' COMMENT '用户性别（0男 1女 2未知）',
  `avatar` varchar(100) DEFAULT '' COMMENT '头像地址',
  `password` varchar(100) DEFAULT '' COMMENT '密码',
  `status` char(1) DEFAULT '0' COMMENT '账号状态（0正常 1停用）',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `login_ip` varchar(128) DEFAULT '' COMMENT '最后登录IP',
  `login_date` datetime DEFAULT NULL COMMENT '最后登录时间',
  `pwd_update_date` datetime DEFAULT NULL COMMENT '密码最后更新时间',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=104 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_user`
--

LOCK TABLES `sys_user` WRITE;
/*!40000 ALTER TABLE `sys_user` DISABLE KEYS */;
INSERT INTO `sys_user` VALUES (1,103,'admin','若依','00','<EMAIL>','15888888888','1','','$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2','0','0','127.0.0.1','2025-07-10 13:01:45','2025-07-07 12:40:27','admin','2025-07-07 12:40:27','','2025-07-10 13:01:44','管理员'),(2,105,'ry','若依','00','<EMAIL>','15666666666','1','','$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2','0','2','127.0.0.1','2025-07-07 12:40:27','2025-07-07 12:40:27','admin','2025-07-07 12:40:27','',NULL,'测试员'),(100,NULL,'tuke','tuke','00','','','0','','$2a$10$SP/SV47njXxaBMhAoYFCOOuafQkfnJ4PCcoc2I6hlxqRkFwYGjVxi','0','2','',NULL,'2025-07-07 12:57:41','','2025-07-07 12:57:41','',NULL,NULL),(101,NULL,'tuke','tuke','00','','15316837280','0','','$2a$10$gfgh5zK2yLICsYXSQvb15upjgTAsQLFT9tu84FJWty6bJq59.vFFq','0','2','',NULL,'2025-07-07 13:02:12','','2025-07-07 13:02:12','',NULL,NULL),(102,NULL,'tuke','tuke','00','','15316837280','0','','$2a$10$Kdu1VHq22E/KAxcS1edCOOm7GlIeB9ZUywdyudIKcXpOw9qkvLXAy','0','2','',NULL,'2025-07-07 13:04:06','','2025-07-07 13:04:06','',NULL,NULL),(103,NULL,'tuke','tuke','00','','15316837280','0','/profile/avatar/2025/07/07/71fa11da96bb4e20b42197ec5d198232.png','$2a$10$94QlBlwYgGLJ5gCg8Ik9k.PYui7PrIcK80Xu5nQL9KxFtNSMmUjdK','0','2','127.0.0.1','2025-07-09 12:58:54','2025-07-07 13:05:39','','2025-07-07 13:05:38','system','2025-07-09 12:58:54',NULL);
/*!40000 ALTER TABLE `sys_user` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_user_post`
--

DROP TABLE IF EXISTS `sys_user_post`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_user_post` (
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `post_id` bigint NOT NULL COMMENT '岗位ID',
  PRIMARY KEY (`user_id`,`post_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户与岗位关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_user_post`
--

LOCK TABLES `sys_user_post` WRITE;
/*!40000 ALTER TABLE `sys_user_post` DISABLE KEYS */;
INSERT INTO `sys_user_post` VALUES (1,1);
/*!40000 ALTER TABLE `sys_user_post` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_user_role`
--

DROP TABLE IF EXISTS `sys_user_role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_user_role` (
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `role_id` bigint NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`user_id`,`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户和角色关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_user_role`
--

LOCK TABLES `sys_user_role` WRITE;
/*!40000 ALTER TABLE `sys_user_role` DISABLE KEYS */;
INSERT INTO `sys_user_role` VALUES (1,1);
/*!40000 ALTER TABLE `sys_user_role` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-10 13:22:39
