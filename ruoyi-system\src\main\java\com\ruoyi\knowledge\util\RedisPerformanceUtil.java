package com.ruoyi.knowledge.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Redis性能监控和优化工具类
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
@Component
public class RedisPerformanceUtil {

    private static final Logger logger = LoggerFactory.getLogger(RedisPerformanceUtil.class);

    @Value("${spring.redis.host:localhost}")
    private String redisHost;

    @Value("${spring.redis.port:6379}")
    private int redisPort;

    @Value("${spring.redis.password:}")
    private String redisPassword;

    @Value("${spring.redis.database:0}")
    private int redisDatabase;

    @Value("${knowledge.embedding.store.type:memory}")
    private String storeType;

    private JedisPool jedisPool;

    @PostConstruct
    public void init() {
        if ("redis".equalsIgnoreCase(storeType)) {
            initJedisPool();
        }
    }

    private void initJedisPool() {
        try {
            JedisPoolConfig config = new JedisPoolConfig();
            config.setMaxTotal(32);
            config.setMaxIdle(16);
            config.setMinIdle(2);
            config.setTestOnBorrow(true);
            config.setTestOnReturn(true);

            if (redisPassword != null && !redisPassword.trim().isEmpty()) {
                jedisPool = new JedisPool(config, redisHost, redisPort, 30000, redisPassword, redisDatabase);
            } else {
                jedisPool = new JedisPool(config, redisHost, redisPort, 30000, null, redisDatabase);
            }
        } catch (Exception e) {
            logger.error("Redis连接池初始化失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取Redis性能指标
     */
    public Map<String, Object> getPerformanceMetrics() {
        Map<String, Object> metrics = new HashMap<>();
        
        if (jedisPool == null) {
            metrics.put("error", "Redis连接池未初始化");
            return metrics;
        }

        try (Jedis jedis = jedisPool.getResource()) {
            // 获取内存信息
            String memoryInfo = jedis.info("memory");
            metrics.put("memory", parseRedisInfo(memoryInfo));

            // 获取统计信息
            String statsInfo = jedis.info("stats");
            metrics.put("stats", parseRedisInfo(statsInfo));

            // 获取客户端信息
            String clientsInfo = jedis.info("clients");
            metrics.put("clients", parseRedisInfo(clientsInfo));

            // 获取服务器信息
            String serverInfo = jedis.info("server");
            metrics.put("server", parseRedisInfo(serverInfo));

            // 获取持久化信息
            String persistenceInfo = jedis.info("persistence");
            metrics.put("persistence", parseRedisInfo(persistenceInfo));

            // 连接池状态
            metrics.put("pool", getPoolMetrics());

        } catch (Exception e) {
            logger.error("获取Redis性能指标失败: {}", e.getMessage(), e);
            metrics.put("error", e.getMessage());
        }

        return metrics;
    }

    /**
     * 解析Redis INFO命令返回的信息
     */
    private Map<String, String> parseRedisInfo(String info) {
        Map<String, String> result = new HashMap<>();
        String[] lines = info.split("\r\n");
        
        for (String line : lines) {
            if (line.contains(":") && !line.startsWith("#")) {
                String[] parts = line.split(":", 2);
                if (parts.length == 2) {
                    result.put(parts[0].trim(), parts[1].trim());
                }
            }
        }
        
        return result;
    }

    /**
     * 获取连接池指标
     */
    private Map<String, Object> getPoolMetrics() {
        Map<String, Object> poolMetrics = new HashMap<>();
        
        if (jedisPool != null) {
            poolMetrics.put("numActive", jedisPool.getNumActive());
            poolMetrics.put("numIdle", jedisPool.getNumIdle());
            poolMetrics.put("numWaiters", jedisPool.getNumWaiters());
            poolMetrics.put("isClosed", jedisPool.isClosed());
        }
        
        return poolMetrics;
    }

    /**
     * 检查Redis配置并提供优化建议
     */
    public Map<String, Object> getOptimizationSuggestions() {
        Map<String, Object> suggestions = new HashMap<>();
        
        if (jedisPool == null) {
            suggestions.put("error", "Redis连接池未初始化");
            return suggestions;
        }

        try (Jedis jedis = jedisPool.getResource()) {
            Map<String, String> memoryInfo = parseRedisInfo(jedis.info("memory"));
            Map<String, String> serverInfo = parseRedisInfo(jedis.info("server"));
            
            // 内存使用建议
            String usedMemory = memoryInfo.get("used_memory_human");
            String maxMemory = memoryInfo.get("maxmemory_human");
            
            if (maxMemory == null || "0B".equals(maxMemory)) {
                suggestions.put("memory_limit", "建议设置Redis最大内存限制，防止内存溢出");
            }

            // 持久化建议
            Map<String, String> persistenceInfo = parseRedisInfo(jedis.info("persistence"));
            String aofEnabled = persistenceInfo.get("aof_enabled");
            String rdbLastSaveTime = persistenceInfo.get("rdb_last_save_time");
            
            if (!"1".equals(aofEnabled)) {
                suggestions.put("persistence", "建议启用AOF持久化以确保数据安全");
            }

            // 版本检查
            String redisVersion = serverInfo.get("redis_version");
            if (redisVersion != null) {
                String[] versionParts = redisVersion.split("\\.");
                if (versionParts.length >= 2) {
                    int majorVersion = Integer.parseInt(versionParts[0]);
                    int minorVersion = Integer.parseInt(versionParts[1]);
                    
                    if (majorVersion < 6 || (majorVersion == 6 && minorVersion < 2)) {
                        suggestions.put("version", "建议升级到Redis 6.2+以获得更好的RediSearch支持");
                    }
                }
            }

            // 连接数检查
            Map<String, String> clientsInfo = parseRedisInfo(jedis.info("clients"));
            String connectedClients = clientsInfo.get("connected_clients");
            if (connectedClients != null) {
                int clients = Integer.parseInt(connectedClients);
                if (clients > 100) {
                    suggestions.put("connections", "连接数较多(" + clients + ")，建议检查连接池配置");
                }
            }

            // 检查RediSearch模块
            try {
                List<Object> modules = jedis.sendCommand(() -> "MODULE".getBytes(), "LIST".getBytes());
                boolean hasSearch = false;
                for (Object module : modules) {
                    if (module instanceof List) {
                        List<?> moduleInfo = (List<?>) module;
                        if (moduleInfo.size() >= 2 && "search".equals(moduleInfo.get(1))) {
                            hasSearch = true;
                            break;
                        }
                    }
                }
                
                if (!hasSearch) {
                    suggestions.put("redisearch", "未检测到RediSearch模块，请安装Redis Stack");
                }
            } catch (Exception e) {
                suggestions.put("redisearch", "无法检查RediSearch模块状态");
            }

        } catch (Exception e) {
            logger.error("获取优化建议失败: {}", e.getMessage(), e);
            suggestions.put("error", e.getMessage());
        }

        return suggestions;
    }

    /**
     * 执行性能测试
     */
    public Map<String, Object> performanceTest() {
        Map<String, Object> testResults = new HashMap<>();
        
        if (jedisPool == null) {
            testResults.put("error", "Redis连接池未初始化");
            return testResults;
        }

        try (Jedis jedis = jedisPool.getResource()) {
            // 测试基本操作延迟
            long startTime = System.currentTimeMillis();
            for (int i = 0; i < 100; i++) {
                jedis.set("test_key_" + i, "test_value_" + i);
            }
            long setTime = System.currentTimeMillis() - startTime;
            testResults.put("set_100_keys_ms", setTime);

            startTime = System.currentTimeMillis();
            for (int i = 0; i < 100; i++) {
                jedis.get("test_key_" + i);
            }
            long getTime = System.currentTimeMillis() - startTime;
            testResults.put("get_100_keys_ms", getTime);

            // 清理测试数据
            for (int i = 0; i < 100; i++) {
                jedis.del("test_key_" + i);
            }

            // 测试向量索引操作（如果支持）
            try {
                startTime = System.currentTimeMillis();
                jedis.sendCommand(() -> "FT.CREATE".getBytes(), 
                    "test_perf_idx".getBytes(),
                    "ON".getBytes(), "HASH".getBytes(),
                    "PREFIX".getBytes(), "1".getBytes(), "perf:".getBytes(),
                    "SCHEMA".getBytes(),
                    "vector".getBytes(), "VECTOR".getBytes(), "FLAT".getBytes(),
                    "6".getBytes(), "TYPE".getBytes(), "FLOAT32".getBytes(),
                    "DIM".getBytes(), "3".getBytes(),
                    "DISTANCE_METRIC".getBytes(), "L2".getBytes());
                
                long createIndexTime = System.currentTimeMillis() - startTime;
                testResults.put("create_vector_index_ms", createIndexTime);

                // 清理测试索引
                jedis.sendCommand(() -> "FT.DROPINDEX".getBytes(), "test_perf_idx".getBytes());
                
            } catch (Exception e) {
                testResults.put("vector_index_error", "向量索引测试失败: " + e.getMessage());
            }

        } catch (Exception e) {
            logger.error("性能测试失败: {}", e.getMessage(), e);
            testResults.put("error", e.getMessage());
        }

        return testResults;
    }

    /**
     * 关闭连接池
     */
    public void close() {
        if (jedisPool != null && !jedisPool.isClosed()) {
            jedisPool.close();
        }
    }
}
