package com.ruoyi.knowledge.service.impl;

import com.ruoyi.knowledge.domain.KnowledgeBase;
import com.ruoyi.knowledge.domain.KnowledgeDocument;
import com.ruoyi.knowledge.service.IKnowledgeBaseService;
import com.ruoyi.knowledge.service.IKnowledgeDocumentService;
import com.ruoyi.knowledge.service.IKnowledgeBaseMigrationService;
import com.ruoyi.knowledge.service.IKnowledgeRagService;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.store.embedding.EmbeddingStore;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 知识库迁移服务实现
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
@Service
public class KnowledgeBaseMigrationServiceImpl implements IKnowledgeBaseMigrationService {

    private static final Logger logger = LoggerFactory.getLogger(KnowledgeBaseMigrationServiceImpl.class);

    @Autowired
    private IKnowledgeBaseService knowledgeBaseService;

    @Autowired
    private IKnowledgeDocumentService knowledgeDocumentService;

    @Autowired
    private IKnowledgeRagService knowledgeRagService;

    @Autowired
    private EmbeddingStore<TextSegment> embeddingStore;

    @Value("${knowledge.embedding.store.type:memory}")
    private String storeType;

    // 迁移进度跟踪
    private final AtomicInteger totalKnowledgeBases = new AtomicInteger(0);
    private final AtomicInteger processedKnowledgeBases = new AtomicInteger(0);
    private final AtomicInteger successfulMigrations = new AtomicInteger(0);
    private final AtomicInteger failedMigrations = new AtomicInteger(0);
    private final AtomicLong totalDocuments = new AtomicLong(0);
    private final AtomicLong processedDocuments = new AtomicLong(0);

    @Override
    public List<Long> getAllKnowledgeBasesToMigrate() {
        try {
            // 获取所有知识库
            KnowledgeBase queryParam = new KnowledgeBase();
            queryParam.setStatus("0"); // 只获取正常状态的知识库
            List<KnowledgeBase> knowledgeBases = knowledgeBaseService.selectKnowledgeBaseList(queryParam);
            
            List<Long> knowledgeBaseIds = new ArrayList<>();
            for (KnowledgeBase kb : knowledgeBases) {
                knowledgeBaseIds.add(kb.getId());
            }
            
            logger.info("找到 {} 个需要迁移的知识库", knowledgeBaseIds.size());
            return knowledgeBaseIds;
        } catch (Exception e) {
            logger.error("获取需要迁移的知识库失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public boolean migrateKnowledgeBase(Long knowledgeBaseId) {
        try {
            logger.info("开始迁移知识库: {}", knowledgeBaseId);

            // 检查知识库是否存在
            KnowledgeBase knowledgeBase = knowledgeBaseService.selectKnowledgeBaseById(knowledgeBaseId);
            if (knowledgeBase == null) {
                logger.error("知识库不存在: {}", knowledgeBaseId);
                return false;
            }

            // 获取知识库中的所有文档
            KnowledgeDocument queryParam = new KnowledgeDocument();
            queryParam.setKnowledgeBaseId(knowledgeBaseId);
            queryParam.setStatus("0"); // 只获取正常状态的文档
            List<KnowledgeDocument> documents = knowledgeDocumentService.selectKnowledgeDocumentList(queryParam);

            if (documents.isEmpty()) {
                logger.warn("知识库 {} 中没有文档，跳过迁移", knowledgeBaseId);
                return true;
            }

            logger.info("知识库 {} 中有 {} 个文档需要迁移", knowledgeBaseId, documents.size());

            // 重新处理每个文档
            int successCount = 0;
            for (KnowledgeDocument document : documents) {
                try {
                    boolean success = knowledgeRagService.addDocumentToKnowledgeBase(knowledgeBaseId, document);
                    if (success) {
                        successCount++;
                        logger.debug("文档 {} 迁移成功", document.getName());
                    } else {
                        logger.warn("文档 {} 迁移失败", document.getName());
                    }
                    processedDocuments.incrementAndGet();
                } catch (Exception e) {
                    logger.error("迁移文档 {} 时发生异常: {}", document.getName(), e.getMessage(), e);
                }
            }

            boolean migrationSuccess = successCount == documents.size();
            logger.info("知识库 {} 迁移完成，成功: {}/{}", knowledgeBaseId, successCount, documents.size());

            if (migrationSuccess) {
                successfulMigrations.incrementAndGet();
            } else {
                failedMigrations.incrementAndGet();
            }

            processedKnowledgeBases.incrementAndGet();
            return migrationSuccess;

        } catch (Exception e) {
            logger.error("迁移知识库 {} 失败: {}", knowledgeBaseId, e.getMessage(), e);
            failedMigrations.incrementAndGet();
            processedKnowledgeBases.incrementAndGet();
            return false;
        }
    }

    @Override
    public Map<String, Object> migrateAllKnowledgeBases() {
        logger.info("开始批量迁移所有知识库");

        // 重置进度计数器
        totalKnowledgeBases.set(0);
        processedKnowledgeBases.set(0);
        successfulMigrations.set(0);
        failedMigrations.set(0);
        totalDocuments.set(0);
        processedDocuments.set(0);

        Map<String, Object> result = new HashMap<>();
        
        try {
            // 检查存储类型
            if (!"redis".equalsIgnoreCase(storeType)) {
                result.put("success", false);
                result.put("message", "当前配置不是Redis存储，无需迁移");
                return result;
            }

            // 获取所有需要迁移的知识库
            List<Long> knowledgeBaseIds = getAllKnowledgeBasesToMigrate();
            totalKnowledgeBases.set(knowledgeBaseIds.size());

            if (knowledgeBaseIds.isEmpty()) {
                result.put("success", true);
                result.put("message", "没有需要迁移的知识库");
                return result;
            }

            // 计算总文档数
            for (Long knowledgeBaseId : knowledgeBaseIds) {
                KnowledgeDocument queryParam = new KnowledgeDocument();
                queryParam.setKnowledgeBaseId(knowledgeBaseId);
                queryParam.setStatus("0");
                List<KnowledgeDocument> documents = knowledgeDocumentService.selectKnowledgeDocumentList(queryParam);
                totalDocuments.addAndGet(documents.size());
            }

            logger.info("总共需要迁移 {} 个知识库，包含 {} 个文档", totalKnowledgeBases.get(), totalDocuments.get());

            // 清理Redis中的现有数据（可选）
            // 注意：如果需要清理现有数据，请取消下面的注释
            // clearRedisVectorData();

            // 逐个迁移知识库
            for (Long knowledgeBaseId : knowledgeBaseIds) {
                migrateKnowledgeBase(knowledgeBaseId);
            }

            // 构建结果
            result.put("success", true);
            result.put("totalKnowledgeBases", totalKnowledgeBases.get());
            result.put("processedKnowledgeBases", processedKnowledgeBases.get());
            result.put("successfulMigrations", successfulMigrations.get());
            result.put("failedMigrations", failedMigrations.get());
            result.put("totalDocuments", totalDocuments.get());
            result.put("processedDocuments", processedDocuments.get());
            result.put("message", String.format("迁移完成，成功: %d/%d 知识库，处理文档: %d/%d", 
                successfulMigrations.get(), totalKnowledgeBases.get(),
                processedDocuments.get(), totalDocuments.get()));

            logger.info("批量迁移完成: {}", result.get("message"));
            return result;

        } catch (Exception e) {
            logger.error("批量迁移失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "迁移过程中发生异常: " + e.getMessage());
            return result;
        }
    }

    @Override
    public boolean validateMigration(Long knowledgeBaseId) {
        try {
            logger.info("验证知识库 {} 的迁移结果", knowledgeBaseId);

            // 执行一个简单的搜索测试
            List<String> searchResults = knowledgeRagService.searchInKnowledgeBase(knowledgeBaseId, "测试", 1);
            
            // 检查是否能够正常搜索（不要求有结果，只要不抛异常即可）
            logger.info("知识库 {} 搜索测试完成，结果数量: {}", knowledgeBaseId, searchResults.size());
            return true;

        } catch (Exception e) {
            logger.error("验证知识库 {} 迁移结果失败: {}", knowledgeBaseId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean clearRedisVectorData() {
        try {
            if (!"redis".equalsIgnoreCase(storeType)) {
                logger.warn("当前不是Redis存储，无需清理");
                return true;
            }

            logger.info("开始清理Redis中的向量数据");
            
            // 注意：这里需要根据实际的EmbeddingStore实现来清理数据
            // RedisEmbeddingStore可能没有直接的清理所有数据的方法
            // 可能需要通过Redis客户端直接操作
            
            logger.warn("Redis向量数据清理功能需要根据具体实现来完成");
            return true;

        } catch (Exception e) {
            logger.error("清理Redis向量数据失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getMigrationProgress() {
        Map<String, Object> progress = new HashMap<>();
        progress.put("totalKnowledgeBases", totalKnowledgeBases.get());
        progress.put("processedKnowledgeBases", processedKnowledgeBases.get());
        progress.put("successfulMigrations", successfulMigrations.get());
        progress.put("failedMigrations", failedMigrations.get());
        progress.put("totalDocuments", totalDocuments.get());
        progress.put("processedDocuments", processedDocuments.get());
        
        // 计算进度百分比
        int totalKb = totalKnowledgeBases.get();
        int processedKb = processedKnowledgeBases.get();
        double kbProgress = totalKb > 0 ? (double) processedKb / totalKb * 100 : 0;
        progress.put("knowledgeBaseProgress", Math.round(kbProgress * 100.0) / 100.0);
        
        long totalDoc = totalDocuments.get();
        long processedDoc = processedDocuments.get();
        double docProgress = totalDoc > 0 ? (double) processedDoc / totalDoc * 100 : 0;
        progress.put("documentProgress", Math.round(docProgress * 100.0) / 100.0);
        
        return progress;
    }

    @Override
    public boolean rebuildKnowledgeBaseIndex(Long knowledgeBaseId) {
        try {
            logger.info("重建知识库 {} 的索引", knowledgeBaseId);
            
            // 这实际上就是重新迁移知识库
            return migrateKnowledgeBase(knowledgeBaseId);
            
        } catch (Exception e) {
            logger.error("重建知识库 {} 索引失败: {}", knowledgeBaseId, e.getMessage(), e);
            return false;
        }
    }
}
