package com.ruoyi.web.controller.knowledge;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.knowledge.service.IKnowledgeBaseMigrationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 知识库迁移控制器
 * 用于从内存存储迁移到Redis存储
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
@RestController
@RequestMapping("/knowledge/migration")
public class KnowledgeMigrationController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(KnowledgeMigrationController.class);

    @Autowired
    private IKnowledgeBaseMigrationService migrationService;

    @Value("${knowledge.embedding.store.type:memory}")
    private String storeType;

    /**
     * 获取所有需要迁移的知识库
     */
    @PreAuthorize("@ss.hasPermi('knowledge:migration:list')")
    @GetMapping("/list")
    public AjaxResult getKnowledgeBasesToMigrate() {
        try {
            List<Long> knowledgeBaseIds = migrationService.getAllKnowledgeBasesToMigrate();
            return AjaxResult.success("获取成功", knowledgeBaseIds);
        } catch (Exception e) {
            logger.error("获取需要迁移的知识库失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 迁移单个知识库
     */
    @PreAuthorize("@ss.hasPermi('knowledge:migration:migrate')")
    @Log(title = "迁移知识库", businessType = BusinessType.UPDATE)
    @PostMapping("/migrate/{knowledgeBaseId}")
    public AjaxResult migrateKnowledgeBase(@PathVariable Long knowledgeBaseId) {
        try {
            if (!"redis".equalsIgnoreCase(storeType)) {
                return AjaxResult.error("当前配置不是Redis存储，无需迁移");
            }

            logger.info("开始迁移知识库: {}", knowledgeBaseId);
            boolean success = migrationService.migrateKnowledgeBase(knowledgeBaseId);
            
            if (success) {
                return AjaxResult.success("知识库迁移成功");
            } else {
                return AjaxResult.error("知识库迁移失败");
            }
        } catch (Exception e) {
            logger.error("迁移知识库异常: {}", e.getMessage(), e);
            return AjaxResult.error("迁移异常: " + e.getMessage());
        }
    }

    /**
     * 批量迁移所有知识库
     */
    @PreAuthorize("@ss.hasPermi('knowledge:migration:migrate-all')")
    @Log(title = "批量迁移知识库", businessType = BusinessType.UPDATE)
    @PostMapping("/migrate-all")
    public AjaxResult migrateAllKnowledgeBases() {
        try {
            if (!"redis".equalsIgnoreCase(storeType)) {
                return AjaxResult.error("当前配置不是Redis存储，无需迁移");
            }

            logger.info("开始批量迁移所有知识库");
            Map<String, Object> result = migrationService.migrateAllKnowledgeBases();
            
            boolean success = (Boolean) result.get("success");
            if (success) {
                return AjaxResult.success(result.get("message").toString(), result);
            } else {
                return AjaxResult.error(result.get("message").toString(), result);
            }
        } catch (Exception e) {
            logger.error("批量迁移知识库异常: {}", e.getMessage(), e);
            return AjaxResult.error("批量迁移异常: " + e.getMessage());
        }
    }

    /**
     * 验证知识库迁移结果
     */
    @PreAuthorize("@ss.hasPermi('knowledge:migration:validate')")
    @PostMapping("/validate/{knowledgeBaseId}")
    public AjaxResult validateMigration(@PathVariable Long knowledgeBaseId) {
        try {
            if (!"redis".equalsIgnoreCase(storeType)) {
                return AjaxResult.error("当前配置不是Redis存储，无需验证");
            }

            boolean success = migrationService.validateMigration(knowledgeBaseId);
            
            if (success) {
                return AjaxResult.success("知识库迁移验证通过");
            } else {
                return AjaxResult.error("知识库迁移验证失败");
            }
        } catch (Exception e) {
            logger.error("验证知识库迁移异常: {}", e.getMessage(), e);
            return AjaxResult.error("验证异常: " + e.getMessage());
        }
    }

    /**
     * 获取迁移进度
     */
    @PreAuthorize("@ss.hasPermi('knowledge:migration:progress')")
    @GetMapping("/progress")
    public AjaxResult getMigrationProgress() {
        try {
            Map<String, Object> progress = migrationService.getMigrationProgress();
            return AjaxResult.success("获取进度成功", progress);
        } catch (Exception e) {
            logger.error("获取迁移进度异常: {}", e.getMessage(), e);
            return AjaxResult.error("获取进度异常: " + e.getMessage());
        }
    }

    /**
     * 清理Redis向量数据
     */
    @PreAuthorize("@ss.hasPermi('knowledge:migration:clear')")
    @Log(title = "清理Redis向量数据", businessType = BusinessType.DELETE)
    @PostMapping("/clear-redis")
    public AjaxResult clearRedisVectorData() {
        try {
            if (!"redis".equalsIgnoreCase(storeType)) {
                return AjaxResult.error("当前配置不是Redis存储，无需清理");
            }

            boolean success = migrationService.clearRedisVectorData();
            
            if (success) {
                return AjaxResult.success("Redis向量数据清理成功");
            } else {
                return AjaxResult.error("Redis向量数据清理失败");
            }
        } catch (Exception e) {
            logger.error("清理Redis向量数据异常: {}", e.getMessage(), e);
            return AjaxResult.error("清理异常: " + e.getMessage());
        }
    }

    /**
     * 重建知识库索引
     */
    @PreAuthorize("@ss.hasPermi('knowledge:migration:rebuild')")
    @Log(title = "重建知识库索引", businessType = BusinessType.UPDATE)
    @PostMapping("/rebuild/{knowledgeBaseId}")
    public AjaxResult rebuildKnowledgeBaseIndex(@PathVariable Long knowledgeBaseId) {
        try {
            if (!"redis".equalsIgnoreCase(storeType)) {
                return AjaxResult.error("当前配置不是Redis存储，无需重建索引");
            }

            boolean success = migrationService.rebuildKnowledgeBaseIndex(knowledgeBaseId);
            
            if (success) {
                return AjaxResult.success("知识库索引重建成功");
            } else {
                return AjaxResult.error("知识库索引重建失败");
            }
        } catch (Exception e) {
            logger.error("重建知识库索引异常: {}", e.getMessage(), e);
            return AjaxResult.error("重建索引异常: " + e.getMessage());
        }
    }

    /**
     * 获取当前存储配置信息
     */
    @PreAuthorize("@ss.hasPermi('knowledge:migration:config')")
    @GetMapping("/config")
    public AjaxResult getStorageConfig() {
        try {
            Map<String, Object> config = new java.util.HashMap<>();
            config.put("storeType", storeType);
            config.put("isRedisStorage", "redis".equalsIgnoreCase(storeType));
            config.put("needMigration", !"redis".equalsIgnoreCase(storeType));
            
            return AjaxResult.success("获取配置成功", config);
        } catch (Exception e) {
            logger.error("获取存储配置异常: {}", e.getMessage(), e);
            return AjaxResult.error("获取配置异常: " + e.getMessage());
        }
    }
}
