# Redis Stack 安装配置指南

## 概述

为了支持知识库的向量存储和搜索功能，需要安装Redis Stack或在现有Redis上添加RediSearch模块。

## 方案选择

### 方案一：安装Redis Stack（推荐）

Redis Stack包含了RediSearch、RedisJSON等模块，是最简单的解决方案。

#### Windows安装

1. **下载Redis Stack**
   ```bash
   # 访问官网下载
   https://redis.io/download#redis-stack
   
   # 或使用Chocolatey安装
   choco install redis-stack-server
   ```

2. **启动Redis Stack**
   ```bash
   # 默认启动
   redis-stack-server
   
   # 指定配置文件启动
   redis-stack-server --loadmodule /path/to/redisearch.so
   ```

3. **验证安装**
   ```bash
   redis-cli
   127.0.0.1:6379> MODULE LIST
   # 应该看到redisearch模块
   ```

#### Linux安装

1. **Ubuntu/Debian**
   ```bash
   curl -fsSL https://packages.redis.io/gpg | sudo gpg --dearmor -o /usr/share/keyrings/redis-archive-keyring.gpg
   echo "deb [signed-by=/usr/share/keyrings/redis-archive-keyring.gpg] https://packages.redis.io/deb $(lsb_release -cs) main" | sudo tee /etc/apt/sources.list.d/redis.list
   sudo apt-get update
   sudo apt-get install redis-stack-server
   ```

2. **CentOS/RHEL**
   ```bash
   curl -fsSL https://packages.redis.io/gpg | sudo gpg --dearmor -o /etc/pki/rpm-gpg/RPM-GPG-KEY-redis
   echo '[redis]
   name=Redis
   baseurl=https://packages.redis.io/rpm/rhel7
   enabled=1
   gpgcheck=1
   gpgkey=file:///etc/pki/rpm-gpg/RPM-GPG-KEY-redis' | sudo tee /etc/yum.repos.d/redis.repo
   sudo yum install redis-stack-server
   ```

### 方案二：Docker安装（推荐用于开发环境）

```bash
# 拉取Redis Stack镜像
docker pull redis/redis-stack:latest

# 启动Redis Stack容器
docker run -d --name redis-stack \
  -p 6379:6379 \
  -p 8001:8001 \
  redis/redis-stack:latest

# 验证
docker exec -it redis-stack redis-cli MODULE LIST
```

### 方案三：现有Redis添加RediSearch模块

如果已有Redis实例，可以添加RediSearch模块：

1. **下载RediSearch模块**
   ```bash
   # 从GitHub下载对应版本
   wget https://github.com/RediSearch/RediSearch/releases/download/v2.8.4/redisearch.Linux-ubuntu18.04-x86_64.2.8.4.zip
   unzip redisearch.Linux-ubuntu18.04-x86_64.2.8.4.zip
   ```

2. **配置Redis加载模块**
   ```bash
   # 在redis.conf中添加
   loadmodule /path/to/redisearch.so
   
   # 或启动时指定
   redis-server --loadmodule /path/to/redisearch.so
   ```

## 配置验证

### 1. 检查模块加载
```bash
redis-cli
127.0.0.1:6379> MODULE LIST
1) 1) "name"
   2) "search"
   3) "ver"
   4) 20804
```

### 2. 测试向量搜索功能
```bash
# 创建向量索引
127.0.0.1:6379> FT.CREATE test_idx ON HASH PREFIX 1 test: SCHEMA vector VECTOR FLAT 6 TYPE FLOAT32 DIM 3 DISTANCE_METRIC L2

# 添加向量数据
127.0.0.1:6379> HSET test:1 vector "\x00\x00\x80\x3f\x00\x00\x00\x40\x00\x00\x40\x40"

# 执行向量搜索
127.0.0.1:6379> FT.SEARCH test_idx "*=>[KNN 1 @vector $query_vec]" PARAMS 2 query_vec "\x00\x00\x80\x3f\x00\x00\x00\x40\x00\x00\x40\x40" DIALECT 2
```

## 生产环境配置建议

### 1. 内存配置
```bash
# redis.conf
maxmemory 4gb
maxmemory-policy allkeys-lru
```

### 2. 持久化配置
```bash
# 启用AOF持久化
appendonly yes
appendfsync everysec

# RDB备份
save 900 1
save 300 10
save 60 10000
```

### 3. 网络配置
```bash
# 绑定地址
bind 127.0.0.1 192.168.1.100

# 端口
port 6379

# 密码保护
requirepass your_strong_password
```

### 4. 性能优化
```bash
# 禁用透明大页
echo never > /sys/kernel/mm/transparent_hugepage/enabled

# 调整内核参数
echo 'vm.overcommit_memory = 1' >> /etc/sysctl.conf
sysctl vm.overcommit_memory=1
```

## 故障排除

### 常见问题

1. **模块加载失败**
   - 检查模块文件路径是否正确
   - 确认Redis版本与模块版本兼容
   - 查看Redis日志获取详细错误信息

2. **向量搜索失败**
   - 确认RediSearch模块版本支持向量搜索（需要2.4+）
   - 检查向量维度配置是否正确
   - 验证数据格式是否符合要求

3. **性能问题**
   - 调整Redis内存配置
   - 优化向量索引参数
   - 考虑使用Redis集群

### 监控命令

```bash
# 查看内存使用
redis-cli INFO memory

# 查看索引信息
redis-cli FT.INFO knowledge_base_index

# 查看连接数
redis-cli INFO clients

# 查看性能统计
redis-cli INFO stats
```

## 下一步

完成Redis Stack安装后，继续进行应用配置修改。
